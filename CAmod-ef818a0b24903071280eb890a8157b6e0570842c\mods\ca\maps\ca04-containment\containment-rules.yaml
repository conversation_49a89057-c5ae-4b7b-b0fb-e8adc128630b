
^Palettes:
	TintPostProcessEffect:
	WeatherOverlay@RAIN:
		WindTick: 300, 550
		UseSquares: false
		ScatterDirection: 0, 0
		Gravity: 35, 45
		SwingOffset: 0, 0
		SwingSpeed: 0, 0
		SwingAmplitude: 0, 0
		ParticleColors: aaaaaa, 999999, aa6666, 66aa66
		LineTailAlphaValue: 12
		ParticleSize: 1, 1
		ParticleDensityFactor: 10

World:
	LuaScript:
		Scripts: campaign.lua, containment.lua
	MissionData:
		Briefing: Intel reports that the Soviets are preparing to strike their ruptured Chronosphere with atomic weapons in an attempt to put an end to the damage that it continues to cause. They don't know what they are dealing with. If they succeed, the consequences could be apocalyptic.\n\nThere isn't enough time to launch a full offensive, so our best option is a small, covert operation to take out their missile silos.\n\nThe bulk of the Soviet forces in the region are busy pushing back the alien invaders, so the bases here are vulnerable to such an attack. If all goes to plan, by the time they realize there's a significant threat it will be too late.\n\nWe have been preparing our own means of containing the Chronosphere, and assuming we succeed in stopping the Soviets from launching, this would be as good a time as any to put it to the test.\n\nFirst, you must take out the three atomic reactors that power the Soviet Tesla Coils so that our team can slip by the remaining defenses.\n\nNext, take out the SAM sites along the shoreline to clear a path for our attempt at containment.\n\nThirdly, destroy the two missile silos that are preparing to launch.\n\nThen we will send in our experimental weapon. Escort it to the Chronosphere and neutralize it.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	ScriptLobbyDropdown@RESPAWN:
		ID: respawn
		Label: Respawns
		Description: Enable/disable respawning on death
		Values:
			enabled: Enabled
			disabled: Disabled
		Default: disabled
		DisplayOrder: 999
	MusicPlaylist:
		StartingMusic: run1226m
	Locomotor@SEAL:
		TerrainSpeeds:
			Rough: 100
			Ford: 100
			Beach: 100
			Water: 100
			Ore: 100

Player:
	PlayerResources:
		DefaultCash: 0

DOG:
	Mobile:
		Speed: 46
	Health:
		HP: 4500

E1:
	ExternalCondition@NORMAL:
		Condition: difficulty-normal
	ExternalCondition@EASY:
		Condition: difficulty-easy
	RangeMultiplier@EASY:
		Modifier: 80
		RequiresCondition: difficulty-easy
	RangeMultiplier@NORMAL:
		Modifier: 90
		RequiresCondition: difficulty-normal

E2:
	Mobile:
		Speed: 46

E3:
	Mobile:
		Speed: 46

E4:
	Mobile:
		Speed: 46

PDOX.CROSSRIP:
	Inherits: PDOX
	Targetable:
		TargetTypes: Temporal
	Tooltip:
		Name: Ruptured Chronosphere
	Health:
		HP: 35000
	Armor:
		Type: Concrete
	-TooltipExtras:
	Buildable:
		Description: Causes temporal and interdimensional crossrips.
	-Valued:
	-WithBuildingBib:
	-WithColoredOverlay@IDISABLE:
	-InfiltrateForSupportPowerReset:
	-Targetable@INFILTRATION:
	-WithMakeAnimation:
	-DetonateWeaponPower@ChronoAI:
	-GrantExternalConditionPowerCA@TimeWarp:
	-CaptureManager:
	-Capturable:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-Sellable:
	-Targetable@HACKCAPTURABLE:
	-GrantConditionOnPrerequisite@OwnedByAi:
	-GrantConditionOnPrerequisite@AiSuperweaponsEnabled:

TSLA:
	-Targetable@TeslaBoost:

SAM:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:

WORMHOLE:
	-Targetable:

SPY:
	RevealsShroud:
		Range: 9c512
	Mobile:
		Speed: 60
	Selectable:
		Priority: 10
	TooltipExtras:
		Attributes: • Disguise as enemy infantry by right-clicking them

^DifficultyModifiers:
	ExternalCondition@NORMAL:
		Condition: difficulty-normal
	ExternalCondition@EASY:
		Condition: difficulty-easy
	DamageMultiplier@EASY:
		Modifier: 80
		RequiresCondition: difficulty-easy
	RevealsShroudMultiplier@NORMALEASY:
		Modifier: 115
		RequiresCondition: difficulty-normal || difficulty-easy
	RangeMultiplier@EASY:
		Modifier: 115
		RequiresCondition: difficulty-easy

SEAL:
	Inherits@DIFFICULTY: ^DifficultyModifiers
	Health:
		HP: 10000
	RevealsShroud:
		Range: 7c0
	AutoTarget:
		InitialStance: HoldFire
	-ChangesHealth@ELITE:
	GainsExperience:
		ExperienceModifier: 250000
	Mobile:
		Speed: 54

LST:
	RejectsOrders:
	-Selectable:
	Interactable:

^GDIPalette:
	RenderSprites:
		Palette: temptd

OCAR.CHPR:
	Inherits: OCAR
	Inherits@GDIPAL: ^GDIPalette
	RejectsOrders:
	-Selectable:
	Interactable:
	Aircraft:
		InitialFacing: 512
	Carryall:
		InitialActor: chpr
	Health:
		HP: 100000

OCAR.Husk:
	Inherits@GDIPAL: ^GDIPalette

SEAS:
	RevealsShroud:
		Range: 4c512
		MinRange: 0
	-RevealsShroud@GAPGEN:

SAPC:
	ExternalCondition@FORCEUNCLOAK:
		Condition: cloak-force-disabled
	AutoTarget:
		InitialStanceAI: HoldFire
	-Targetable:

MSLO:
	NukePower@ABomb:
		DisplayTimerRelationships: None
		AllowMultiple: true
	-InfiltrateForSupportPowerReset:
	-Targetable@INFILTRATION:
	Health:
		HP: 60000

^NukeDummy:
	Inherits@ABOMBPOWER: ^ABombPower
	AlwaysVisible:
	Interactable:
	ScriptTriggers:
	NukePower@ABomb:
		-PauseOnCondition:

NukeDummyEasy:
	Inherits: ^NukeDummy
	NukePower@ABomb:
		ChargeInterval: 60000

NukeDummyNormal:
	Inherits: ^NukeDummy
	NukePower@ABomb:
		ChargeInterval: 45000

NukeDummyHard:
	Inherits: ^NukeDummy
	NukePower@ABomb:
		ChargeInterval: 30000

SPEN:
	-InfiltrateToCreateProxyActor:
	-Targetable@INFILTRATION:

DOME:
	-InfiltrateForExploration:
	-Targetable@INFILTRATION:

WEAP:
	-Targetable@INFILTRATION:
	-InfiltrateToCreateProxyActor:

HPAD:
	-Targetable@INFILTRATION:
	-InfiltrateToCreateProxyActor:

AFLD:
	-Targetable@INFILTRATION:
	-InfiltrateToCreateProxyActor:

POWR:
	-Targetable@PowerOutageInfiltrate:
	-InfiltrateForPowerOutage:

APWR:
	-Targetable@PowerOutageInfiltrate:
	-InfiltrateForPowerOutage:

NPWR:
	-Targetable@PowerOutageInfiltrate:
	-InfiltrateForPowerOutage:

BARR:
	-Targetable@INFILTRATION:
	-InfiltrateToCreateProxyActor@spy:
