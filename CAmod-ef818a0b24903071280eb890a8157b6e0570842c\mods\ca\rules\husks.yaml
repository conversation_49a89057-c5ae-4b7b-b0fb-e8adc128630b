1TNK.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: <PERSON><PERSON> (Scout Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: 1tnk.destroyed

2TNK.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Medium Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: 2tnk.destroyed

GTNK.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Grizzly Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: gtnk.destroyed

3TNK.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Heavy Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: 3tnk.destroyed

3TNK.YURI.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Lasher Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: 3tnky.destroyed

3TNK.RHIN.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Rhino Heavy Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: rhin.destroyed

4TNK.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Mammoth Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: 4tnk.destroyed

4TNK.ERAD.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Eradicator)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: 4tnkerad.destroyed

HARV.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Ore Truck)
	RenderSprites:
		Image: hhusk2

# for RA map compatibility only
HARV.FullHusk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Ore Truck)
	RenderSprites:
		Image: hhusk

# for RA map compatibility only
HARV.EmptyHusk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Ore Truck)
	RenderSprites:
		Image: hhusk2

HARV.Chrono.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Chrono Miner)
	RenderSprites:
		Image: charv.destroyed

MCV.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Mobile Construction Vehicle)
	RenderSprites:
		Image: mcvhusk

MGG.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Mobile Gap Generator)
	ThrowsParticle@spinner:
		Anim: spinner-idle
		Offset: -299,0,171
	RenderSprites:
		Image: mgg.destroyed

MSG.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Mobile Stealth Generator)
	RenderSprites:
		Image: msg.destroyed

MOLE.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Subterranean APC)
	RenderSprites:
		Image: mole.destroyed

TRAN.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Chinook
	Aircraft:
		TurnSpeed: 1
		Speed: 149
	WithIdleOverlay@PRIMARY:
		Offset: -597,0,341
		Sequence: rotor
	WithIdleOverlay@SECONDARY:
		Offset: 597,0,213
		Sequence: rotor2
	RevealsShroud:
		Range: 8c0
		MinRange: 6c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 6c0
		Type: GroundPosition
	RenderSprites:
		Image: tran3

TRAN.Husk.EMP:
	Inherits: TRAN.Husk
	Inherits: ^EmpVisualEffect

TRAN.Husk1:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Chinook)
	RenderSprites:
		Image: tran1husk
	-Capturable:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-GivesCashOnCaptureCA:
	-GrantConditionOnCapture:
	-KillsSelf:

TRAN.Husk2:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Chinook)
	RenderSprites:
		Image: tran2husk
	-Capturable:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-GivesCashOnCaptureCA:
	-GrantConditionOnCapture:
	-KillsSelf:

HALO.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Heavy Transport
	Aircraft:
		TurnSpeed: 1
		Speed: 149
	WithIdleOverlay@PRIMARY:
		Offset: 260,0,343
		Sequence: rotor
	RevealsShroud:
		Range: 8c0
		MinRange: 6c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 6c0
		Type: GroundPosition
	RenderSprites:
		Image: halo

HALO.Husk.EMP:
	Inherits: HALO.Husk
	Inherits: ^EmpVisualEffect

NHAW.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Nighthawk
	Aircraft:
		TurnSpeed: 1
		Speed: 149
	WithIdleOverlay@PRIMARY:
		Offset: 237,0,263
		Sequence: rotor
	WithIdleOverlay@SECONDARY:
		Offset: -997,0,341
		Sequence: rotor2
	RevealsShroud:
		Range: 8c0
		MinRange: 6c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 6c0
		Type: GroundPosition
	RenderSprites:
		Image: nhaw

NHAW.UPG.Husk:
	Inherits: NHAW.Husk
	WithFacingSpriteBody:
		Sequence: idle-upg

NHAW.Husk.EMP:
	Inherits: NHAW.Husk
	Inherits: ^EmpVisualEffect

NHAW.UPG.Husk.EMP:
	Inherits: NHAW.UPG.Husk
	Inherits: ^EmpVisualEffect

BADR.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Badger
	Aircraft:
		TurnSpeed: 5
		Speed: 180
	LeavesTrails@0:
		Offsets: -432,560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	LeavesTrails@1:
		Offsets: -432,-560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: badr
	-RevealOnDeath:

BADR.Husk.EMP:
	Inherits: BADR.Husk
	Inherits: ^EmpVisualEffect

B2B.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: B2 Stealth Bomber
	Aircraft:
		TurnSpeed: 5
		Speed: 180
	LeavesTrails@0:
		Offsets: -432,560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	LeavesTrails@1:
		Offsets: -432,-560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: b2b
	-RevealOnDeath:

B2B.Husk.EMP:
	Inherits: B2B.Husk
	Inherits: ^EmpVisualEffect

MIG.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: MiG Attack Plane
	Contrail@1:
		Offset: -598,-683,0
	Contrail@2:
		Offset: -598,683,0
	Aircraft:
		TurnSpeed: 5
		Speed: 201
	LeavesTrails:
		Offsets: -853,0,171
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 10c0
		Type: GroundPosition
	RenderSprites:
		Image: mig

MIG.Husk.EMP:
	Inherits: MIG.Husk
	Inherits: ^EmpVisualEffect

MIG.Husk.Empty:
	Inherits: MIG.Husk
	Inherits: ^PlaneHuskEmpty

MIG.Husk.Empty.EMP:
	Inherits: MIG.Husk.Empty
	Inherits: ^EmpVisualEffect

YAK.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Yak Attack Plane
	Aircraft:
		TurnSpeed: 5
		Speed: 180
	LeavesTrails:
		Offsets: -853,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -98,-683,-10
	Contrail@2:
		Offset: -98,683,-10
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: yak
	FallsToEarth:
		Explosion: UnitExplodePlaneLight

YAK.Husk.EMP:
	Inherits: YAK.Husk
	Inherits: ^EmpVisualEffect

P51.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: P51 Attack Plane
	Aircraft:
		TurnSpeed: 5
		Speed: 180
	LeavesTrails:
		Offsets: -853,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -78,-623,30
	Contrail@2:
		Offset: -78,623,30
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: p51
	FallsToEarth:
		Explosion: UnitExplodePlaneLight

P51.Husk.EMP:
	Inherits: P51.Husk
	Inherits: ^EmpVisualEffect

HELI.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Longbow
	Aircraft:
		TurnSpeed: 4
		Speed: 149
	WithIdleOverlay:
		Offset: 0,0,85
		Sequence: rotor
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 10c0
		Type: GroundPosition
	RenderSprites:
		Image: heli

HELI.Husk.EMP:
	Inherits: HELI.Husk
	Inherits: ^EmpVisualEffect

HELI.Husk.Empty:
	Inherits: ^HelicopterHuskEmpty
	Inherits: HELI.Husk

HELI.Husk.Empty.EMP:
	Inherits: HELI.Husk.Empty
	Inherits: ^EmpVisualEffect

HIND.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Hind
	Aircraft:
		TurnSpeed: 4
		Speed: 112
	WithIdleOverlay:
		Sequence: rotor
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: hind

HIND.Husk.EMP:
	Inherits: HIND.Husk
	Inherits: ^EmpVisualEffect

HIND.Husk.Empty:
	Inherits: ^HelicopterHuskEmpty
	Inherits: HIND.Husk

HIND.Husk.Empty.EMP:
	Inherits: HIND.Husk.Empty
	Inherits: ^EmpVisualEffect

SMIG.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Husk (Supersonic Bomber)
	Aircraft:
		TurnSpeed: 7
		Speed: 373
	Contrail@1:
		Offset: -700,683,-100
	Contrail@2:
		Offset: -700,-683,-100
	LeavesTrails:
		Offsets: -1c43,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: smig

SMIG.Husk.EMP:
	Inherits: SMIG.Husk
	Inherits: ^EmpVisualEffect

T01.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T02.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T03.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T04.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: TEMPERAT, SNOW, INTERIOR

T05.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T06.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T07.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T08.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: x_
		Dimensions: 2,1

T09.Husk:
	Inherits: ^TreeHusk
	MapEditorData:
		ExcludeTilesets: TEMPERAT, SNOW, INTERIOR

T10.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ xx
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T11.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ xx
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T12.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T13.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T14.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T15.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T16.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T17.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

T18.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: TEMPERAT, SNOW, INTERIOR, WINTER, BARREN, JUNGLE

TC01.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: INTERIOR

TC02.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: _x_ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

TC03.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: xx_ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

TC04.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: ____ xxx_ x___
		Dimensions: 4,3
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

TC05.Husk:
	Inherits: ^TreeHusk
	Building:
		Footprint: __x_ xxx_ _xx_
		Dimensions: 4,3
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

AMCV.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Mobile Construction Vehicle)
	RenderSprites:
		Image: amcv.destroyed

RTNK.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Mirage Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: rtnk.destroyed

TNKD.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Tank Destroyer)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: tnkd.destroyed

APC2.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (APC)
	RenderSprites:
		Image: apc2.destroyed

HMMV.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Hum-Vee)
	RenderSprites:
		Image: hmmv.destroyed

BGGY.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Fast Attack Buggy)
	RenderSprites:
		Image: bggy.destroyed

MTNK.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Battle Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: mtnk.destroyed

MTNK.DRONE.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Battle Drone)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: drone.destroyed

HTNK.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Mammoth Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: htnk.destroyed

HTNK.ION.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Ion Mammoth Tank)
	ThrowsParticle@turret:
		Anim: turret-ion
	RenderSprites:
		Image: htnk.destroyed

HTNK.HOVER.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Hover Mammoth Tank)
	ThrowsParticle@turret:
		Anim: turret
	WithFacingSpriteBody:
		Sequence: idle-hover
	RenderSprites:
		Image: htnk.destroyed

HTNK.DRONE.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Mammoth Drone)
	ThrowsParticle@turret:
		Anim: turret-drone
	RenderSprites:
		Image: htnk.destroyed

TITN.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Titan)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: titn.destroyed

TITN.RAIL.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Railgun Titan)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: titn.rail.destroyed

JUGG.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Juggernaut)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: jugg.destroyed

AVTR.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Avatar)
	RenderSprites:
		Image: avtr.destroyed

FTNK.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Flame Tank)
	RenderSprites:
		Image: ftnk.destroyed

HFTK.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Heavy Flame Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: hftk.destroyed

BIKE.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Recon Bike)
	RenderSprites:
		Image: bike.destroyed

MSAM.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (MLRS)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: msam.destroyed

STNK.NOD.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Stealth Tank)
	RenderSprites:
		Image: stnknod.destroyed

APCH.Husk:
	Inherits: ^HelicopterHuskTD
	Tooltip:
		Name: Apache
	Aircraft:
		TurnSpeed: 4
		Speed: 186
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: apch

APCH.Husk.EMP:
	Inherits: APCH.Husk
	Inherits: ^EmpVisualEffect

APCH.Husk.Empty:
	Inherits: APCH.Husk
	Inherits: ^HelicopterHuskEmpty

APCH.Husk.Empty.EMP:
	Inherits: APCH.Husk.Empty
	Inherits: ^EmpVisualEffect

ORCA.Husk:
	Inherits: ^HelicopterHuskTD
	Tooltip:
		Name: Orca
	Aircraft:
		TurnSpeed: 4
		Speed: 186
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 10c0
		Type: GroundPosition
	RenderSprites:
		Image: orca

ORCA.Husk.EMP:
	Inherits: ORCA.Husk
	Inherits: ^EmpVisualEffect

ORCA.Husk.Empty:
	Inherits: ORCA.Husk
	Inherits: ^HelicopterHuskEmpty

ORCA.Husk.Empty.EMP:
	Inherits: ORCA.Husk.Empty
	Inherits: ^EmpVisualEffect

OCAR.Husk:
	Inherits: ^HelicopterHuskTD
	Tooltip:
		Name: Orca Carryall
	Aircraft:
		TurnSpeed: 4
		Speed: 46
	FallsToEarth:
		Velocity: 86
		MaximumSpinSpeed: 20
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: orcaca

OCAR.Husk.EMP:
	Inherits: OCAR.Husk
	Inherits: ^EmpVisualEffect

UAV.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: Husk (UAV)
	Aircraft:
		TurnSpeed: 7
		Speed: 123
	Contrail@1:
		Offset: -725,683,0
	Contrail@2:
		Offset: -725,-683,0
	LeavesTrails:
		Offsets: -1c43,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: uav

UAV.Husk.EMP:
	Inherits: UAV.Husk
	Inherits: ^EmpVisualEffect

HARV.TD.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Harvester (Destroyed)
	RenderSprites:
		Image: harv2.destroyed

HARV.TD.UPG.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Stealth Harvester (Destroyed)
	RenderSprites:
		Image: harv2.upg.destroyed

A10.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: Husk (Warthog)
	Aircraft:
		TurnSpeed: 7
		Speed: 180
	Contrail@1:
		Offset: -725,683,0
	Contrail@2:
		Offset: -725,-683,0
	LeavesTrails:
		Offsets: -1c43,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: a10

A10.Husk.EMP:
	Inherits: A10.Husk
	Inherits: ^EmpVisualEffect

A10.Husk.Empty:
	Inherits: A10.Husk
	Inherits: ^PlaneHuskEmpty

A10.Husk.Empty.EMP:
	Inherits: A10.Husk.Empty
	Inherits: ^EmpVisualEffect

YF23.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: Husk (Aurora)
	Aircraft:
		TurnSpeed: 7
		Speed: 240
	Contrail@1:
		Offset: -325,483,0
	Contrail@2:
		Offset: -325,-483,0
	LeavesTrails:
		Offsets: -1c43,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: yf23

YF23.Husk.EMP:
	Inherits: YF23.Husk
	Inherits: ^EmpVisualEffect

AURO.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: Husk (Aurora)
	Aircraft:
		TurnSpeed: 7
		Speed: 180
	Contrail@1:
		Offset: -300,-800,-50
	Contrail@2:
		Offset: -300,800,-50
	LeavesTrails:
		Offsets: -1c43,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: auro

AURO.Husk.EMP:
	Inherits: AURO.Husk
	Inherits: ^EmpVisualEffect

AURO.Husk.Empty:
	Inherits: AURO.Husk
	Inherits: ^PlaneHuskEmpty

AURO.Husk.Empty.EMP:
	Inherits: AURO.Husk.Empty
	Inherits: ^EmpVisualEffect

C17.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: C17
	Aircraft:
		TurnSpeed: 5
		Speed: 236
	LeavesTrails@0:
		Offsets: -432,560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	LeavesTrails@1:
		Offsets: -432,-560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: c17

C17.Husk.EMP:
	Inherits: C17.Husk
	Inherits: ^EmpVisualEffect

GALX.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Transport Aircraft
	Aircraft:
		TurnSpeed: 5
		Speed: 266
	LeavesTrails@0:
		Offsets: -432,560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	LeavesTrails@1:
		Offsets: -432,-560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: galx
		PlayerPalette: playertd

GALX.Husk.EMP:
	Inherits: GALX.Husk
	Inherits: ^EmpVisualEffect

ANTO.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Transport Aircraft
	Aircraft:
		TurnSpeed: 5
		Speed: 266
	LeavesTrails@0:
		Offsets: -432,560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	LeavesTrails@1:
		Offsets: -432,-560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: anto

ANTO.Husk.EMP:
	Inherits: ANTO.Husk
	Inherits: ^EmpVisualEffect

BTR.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (APC)
	RenderSprites:
		Image: BTR.destroyed

RAH.Husk:
	Inherits: ^HelicopterHuskTD
	Tooltip:
		Name: Comanche
	Aircraft:
		TurnSpeed: 4
		Speed: 186
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: rah66

RAH.Husk.EMP:
	Inherits: RAH.Husk
	Inherits: ^EmpVisualEffect

RAH.Husk.Empty:
	Inherits: RAH.Husk
	Inherits: ^HelicopterHuskEmpty

RAH.Husk.Empty.EMP:
	Inherits: RAH.Husk.Empty
	Inherits: ^EmpVisualEffect

KIRO.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Kirov
	Aircraft:
		TurnSpeed: 2
		Speed: 30
	FallsToEarth:
		MaximumSpinSpeed: 0
		Explosion: KirovExplode
	LeavesTrails@0:
		Offsets: -432,560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	LeavesTrails@1:
		Offsets: -432,-560,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: kirov
	SpawnActorOnDeath:
		Actor: KIRO.Husk.Ground

KIRO.Husk.EMP:
	Inherits: KIRO.Husk
	Inherits: ^EmpVisualEffect

KIRO.Husk.Ground:
	Inherits: ^Husk
	Inherits@SHRAPNEL: ^ThrowsShrapnelBig
	RenderSprites:
		Image: kirov
	Tooltip:
		Name: Husk (Kirov)
	KillsSelf:
		Delay: 1
		-RequiresCondition:
	FireWarheadsOnDeath:
		Weapon: KirovExplode
	-OwnerLostAction:
	-CaptureManager:
	-Capturable:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-GivesCashOnCaptureCA:
	-GrantConditionOnCapture:
	WithIdleOverlay@Burns:
		-RequiresCondition:
	ChangesHealth:
		-RequiresCondition:

APC.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (APC)
	RenderSprites:
		Image: apc.destroyed

TTRA.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Tesla Track)
	RenderSprites:
		Image: ttra.destroyed

TTNK.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Tesla Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: ttnk.destroyed

CYCP.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Cyclops)
	RenderSprites:
		Image: cycp.destroyed

BASI.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Basilisk)
	RenderSprites:
		Image: basi.destroyed

ISU.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Siege Tank)
	RenderSprites:
		Image: isu.destroyed

NUKC.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Nuke Cannon)
	RenderSprites:
		Image: nukc.destroyed

DISR.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Disruptor)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: disr.destroyed

WTNK.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Microwave Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: wtnk.destroyed

BATF.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Battle Fortress)
	RenderSprites:
		Image: batf.destroyed

CHPR.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Chrono Prison)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: chpr.destroyed

MISS.Husk:
	Inherits: ^TechHusk
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	WithBuildingBib:
	Tooltip:
		Name: Ruin (Research Centre)
	TransformOnCapture:
		IntoActor: miss
	InfiltrateForTransform:
		IntoActor: miss
	RenderSprites:
		Image: MISS.destroyed

BIO.Husk:
	Inherits: ^TechHusk
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: Ruin (Bio Lab)
	TransformOnCapture:
		IntoActor: bio
	InfiltrateForTransform:
		IntoActor: bio
	RenderSprites:
		Image: BIO.destroyed

HOSP.Husk:
	Inherits: ^TechHusk
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: Ruin (Hospital)
	TransformOnCapture:
		IntoActor: hosp
	InfiltrateForTransform:
		IntoActor: hosp
	RenderSprites:
		Image: HOSP.destroyed

MACS.Husk:
	Inherits: ^TechHusk
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: Ruin (Machine Shop)
	TransformOnCapture:
		IntoActor: macs
	InfiltrateForTransform:
		IntoActor: macs
	RenderSprites:
		Image: MACS.destroyed

FCOM.Husk:
	Inherits: ^TechHusk
	Tooltip:
		Name: Ruin (Forward Command)
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	TransformOnCapture:
		IntoActor: fcom
	InfiltrateForTransform:
		IntoActor: fcom
	RenderSprites:
		Image: FCOM.destroyed
	WithBuildingBib:

OILB.Husk:
	Inherits: ^TechHusk
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: Ruin (Oil Derrick)
	TransformOnCapture:
		IntoActor: oilb
	InfiltrateForTransform:
		IntoActor: oilb
	RenderSprites:
		Image: OILB.destroyed
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	WithIdleOverlay@Burns:
		Offset: -511,0,0

OILR.Husk:
	Inherits: ^TechHusk
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Tooltip:
		Name: Ruin (Oil Refinery)
	TransformOnCapture:
		IntoActor: oilr
	InfiltrateForTransform:
		IntoActor: oilr
	RenderSprites:
		Image: OILR.destroyed
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	WithIdleOverlay@Burns:
		Offset: -511,0,0

HARR.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Harrier
	Aircraft:
		TurnSpeed: 4
		Speed: 201
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -400,-483,0
	Contrail@2:
		Offset: -400,483,0
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 10c0
		Type: GroundPosition
	RenderSprites:
		Image: harr

HARR.Husk.EMP:
	Inherits: HARR.Husk
	Inherits: ^EmpVisualEffect

HARR.Husk.Empty:
	Inherits: HARR.Husk
	Inherits: ^PlaneHuskEmpty

HARR.Husk.Empty.EMP:
	Inherits: HARR.Husk.Empty
	Inherits: ^EmpVisualEffect

HORN.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: Hornet
	Aircraft:
		TurnSpeed: 4
		Speed: 195
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 2c0
		MinRange: 1c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 1c0
		Type: GroundPosition
	RenderSprites:
		Image: horn
	FallsToEarth:
		Explosion: UnitExplodeDrone

HORN.Husk.EMP:
	Inherits: HORN.Husk
	Inherits: ^EmpVisualEffect

HORN.Husk.Empty:
	Inherits: HORN.Husk
	FallsToEarth:
		Explosion: UnitExplodeDroneEmpty

HORN.Husk.Empty.EMP:
	Inherits: HORN.Husk.Empty
	Inherits: ^EmpVisualEffect

SCRN.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: Banshee
	Aircraft:
		TurnSpeed: 4
		Speed: 225
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -300,-183,0
	Contrail@2:
		Offset: -300,183,0
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: scrin

SCRN.Husk.EMP:
	Inherits: SCRN.Husk
	Inherits: ^EmpVisualEffect

SCRN.Husk.Empty:
	Inherits: SCRN.Husk
	Inherits: ^PlaneHuskEmpty

SCRN.Husk.Empty.EMP:
	Inherits: SCRN.Husk.Empty
	Inherits: ^EmpVisualEffect

ORCB.Husk:
	Inherits: ^HelicopterHuskTD
	Tooltip:
		Name: Orca Bomber
	Aircraft:
		TurnSpeed: 4
		Speed: 155
	FallsToEarth:
		Velocity: 86
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: orcab

ORCB.Husk.EMP:
	Inherits: ORCB.Husk
	Inherits: ^EmpVisualEffect

ORCB.Husk.Empty:
	Inherits: ORCB.Husk
	Inherits: ^HelicopterHuskEmpty

ORCB.Husk.Empty.EMP:
	Inherits: ORCB.Husk.Empty
	Inherits: ^EmpVisualEffect

SUK.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Sukhoi Attack Plane
	Contrail@1:
		Offset: -598,-683,-40
	Contrail@2:
		Offset: -598,683,-40
	Aircraft:
		TurnSpeed: 5
		Speed: 225
	LeavesTrails:
		Offsets: -853,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 10c0
		Type: GroundPosition
	RenderSprites:
		Image: suk

SUK.Husk.EMP:
	Inherits: SUK.Husk
	Inherits: ^EmpVisualEffect

SUK.Husk.Empty:
	Inherits: SUK.Husk
	Inherits: ^PlaneHuskEmpty

SUK.Husk.Empty.EMP:
	Inherits: SUK.Husk.Empty
	Inherits: ^EmpVisualEffect

PTNK.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Prism Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: ptnk.destroyed

PCAN.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Prism Cannon)
	RenderSprites:
		Image: pcan.destroyed

WTNK.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Microwave Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: mwtnk.destroyed

LTNK.Husk:
	Inherits: ^HuskTD
	Tooltip:
		Name: Husk (Light Tank)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: ltnk.destroyed

CHPR.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Chrono Prison)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: chpr.destroyed

VENM.Husk:
	Inherits: ^HelicopterHuskTD
	Tooltip:
		Name: Venom
	Aircraft:
		TurnSpeed: 4
		Speed: 186
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 10c0
		Type: GroundPosition
	RenderSprites:
		Image: venm

VENM.Husk.EMP:
	Inherits: VENM.Husk
	Inherits: ^EmpVisualEffect

VENM.Husk.Empty:
	Inherits: VENM.Husk
	Inherits: ^HelicopterHuskEmpty

VENM.Husk.Empty.EMP:
	Inherits: VENM.Husk.Empty
	Inherits: ^EmpVisualEffect

APOC.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Apocalypse)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: apoc.destroyed

APOC.ERAD.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Apocalyptic Eradicator)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: apoc.erad.destroyed

OVLD.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Overlord)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: ovld.destroyed

OVLD.ERAD.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Eradicator Overlord)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: ovld.erad.destroyed

PMAK.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Peacemaker
	Aircraft:
		TurnSpeed: 7
		Speed: 157
	Contrail@1:
		Offset: -300,-920,170
	Contrail@2:
		Offset: -300,920,170
	LeavesTrails:
		Offsets: -1c43,0,0
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RenderSprites:
		Image: pmak

PMAK.Husk.EMP:
	Inherits: PMAK.Husk
	Inherits: ^EmpVisualEffect

PMAK.Husk.Empty:
	Inherits: PMAK.Husk
	Inherits: ^PlaneHuskEmpty

PMAK.Husk.Empty.EMP:
	Inherits: PMAK.Husk.Empty
	Inherits: ^EmpVisualEffect

BEAG.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Black Eagle
	Aircraft:
		TurnSpeed: 4
		Speed: 201
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -50,-650,20
	Contrail@2:
		Offset: -50,650,20
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: beag

BEAG.Husk.EMP:
	Inherits: BEAG.Husk
	Inherits: ^EmpVisualEffect

BEAG.Husk.Empty:
	Inherits: BEAG.Husk
	Inherits: ^PlaneHuskEmpty

BEAG.Husk.Empty.EMP:
	Inherits: BEAG.Husk.Empty
	Inherits: ^EmpVisualEffect

PHAN.Husk:
	Inherits: ^PlaneHusk
	Tooltip:
		Name: Phantom
	Aircraft:
		TurnSpeed: 4
		Speed: 201
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: 50,-680,80
	Contrail@2:
		Offset: 50,680,80
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: phan

PHAN.Husk.EMP:
	Inherits: PHAN.Husk
	Inherits: ^EmpVisualEffect

PHAN.Husk.Empty:
	Inherits: PHAN.Husk
	Inherits: ^PlaneHuskEmpty

PHAN.Husk.Empty.EMP:
	Inherits: PHAN.Husk.Empty
	Inherits: ^EmpVisualEffect

KAMV.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Kamov
	Aircraft:
		TurnSpeed: 4
		Speed: 112
	WithIdleOverlay@ROTORAIR:
		Sequence: rotor
		Offset: 50,550,192
	WithIdleOverlay@ROTORAIR2:
		Sequence: rotor
		Offset: 50,-550,192
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: kamv

KAMV.Husk.EMP:
	Inherits: HIND.Husk
	Inherits: ^EmpVisualEffect

KAMV.Husk.Empty:
	Inherits: ^HelicopterHuskEmpty
	Inherits: HIND.Husk

KAMV.Husk.Empty.EMP:
	Inherits: HIND.Husk.Empty
	Inherits: ^EmpVisualEffect

SHDE.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: Shade
	Aircraft:
		TurnSpeed: 4
		Speed: 225
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -380,-850,-20
	Contrail@2:
		Offset: -380,850,-20
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: shde

SHDE.Husk.EMP:
	Inherits: SHDE.Husk
	Inherits: ^EmpVisualEffect

SHDE.Husk.Empty:
	Inherits: SHDE.Husk
	Inherits: ^PlaneHuskEmpty

SHDE.Husk.Empty.EMP:
	Inherits: SHDE.Husk.Empty
	Inherits: ^EmpVisualEffect

VERT.Husk:
	Inherits: ^PlaneHuskTD
	Tooltip:
		Name: Vertigo
	Aircraft:
		TurnSpeed: 4
		Speed: 157
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -100,-850,25
	Contrail@2:
		Offset: -100,850,25
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: vert

VERT.Husk.EMP:
	Inherits: VERT.Husk
	Inherits: ^EmpVisualEffect

VERT.Husk.Empty:
	Inherits: VERT.Husk
	Inherits: ^PlaneHuskEmpty

VERT.Husk.Empty.EMP:
	Inherits: VERT.Husk.Empty
	Inherits: ^EmpVisualEffect

MCOR.Husk:
	Inherits: ^HelicopterHuskTD
	Tooltip:
		Name: Manticore
		GenericName: Destroyed Aircraft
	Aircraft:
		TurnSpeed: 8
		Speed: 35
	FallsToEarth:
		MaximumSpinSpeed: 0
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: mcor
	FallsToEarth:
		Explosion: KirovExplode

MCOR.Husk.EMP:
	Inherits: MCOR.Husk
	Inherits@EMP: ^EmpVisualEffect

DISC.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Floating Disc
	Aircraft:
		TurnSpeed: 12
		Speed: 112
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: disccrash

DISC.Husk.EMP:
	Inherits: DISC.Husk
	Inherits: ^EmpVisualEffect
