^ExistsInWorld:
	AppearsOnRadar:
	CombatDebugOverlay:
	GivesExperienceCA:
		PlayerExperienceModifier: 1
		ActorExperienceOnDamage: true
	ScriptTriggers:
	RenderDebugState:

^SpriteActor:
	ClassicFacingBodyOrientation:
	QuantizeFacingsFromSequence:
	RenderSprites:

^1x1Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -512, -512
			BottomRight: 512, 512

^1x2Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -512, -1024
			BottomRight: 512, 1024

^2x1Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -1024, -512
			BottomRight: 1024, 512

^2x2Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -1024, -1024
			BottomRight: 1024, 1024

^3x2Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -1536, -1024
			BottomRight: 1536, 1024

^Transport:
	Cargo:
	MassEnterableCargo:
	WithCargoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		CustomPipSequences:
			gray: pip-gray
			yellow: pip-yellow
			red: pip-red
			blue: pip-blue

# for upgrades, proxy powers, stolen tech, spawners etc
^InvisibleDummy:
	AlwaysVisible:
	Interactable:
	ScriptTriggers:

^PrimaryBuilding:
	PrimaryBuilding:
		PrimaryCondition: primary
		SelectionNotification: PrimaryBuildingSelected
		RequiresCondition: !global-multiqueue
	WithDecoration@primary:
		RequiresCondition: primary && !global-multiqueue
		Position: Top
		Margin: 0, 4
		RequiresSelection: true
		Image: pips
		Sequence: tag-primary

^BotBuilding:
	GrantConditionOnBotOwner@BrutalAI:
		Condition: owned-by-brutal-ai
		Bots: brutal
	ProvidesPrerequisite@BrutalAI:
		Prerequisite: is-brutal-ai
		RequiresCondition: owned-by-brutal-ai
	GrantConditionOnBotOwner@VeryHardAI:
		Condition: owned-by-vhard-ai
		Bots: vhard
	ProvidesPrerequisite@VeryHardAI:
		Prerequisite: is-vhard-ai
		RequiresCondition: owned-by-vhard-ai
	GrantConditionOnBotOwner@HardAI:
		Condition: owned-by-hard-ai
		Bots: hard
	ProvidesPrerequisite@HardAI:
		Prerequisite: is-hard-ai
		RequiresCondition: owned-by-hard-ai
	GrantConditionOnBotOwner@NormalAI:
		Condition: owned-by-normal-ai
		Bots: normal
	ProvidesPrerequisite@NormalAI:
		Prerequisite: is-normal-ai
		RequiresCondition: owned-by-normal-ai
	GrantConditionOnBotOwner@EasyAI:
		Condition: owned-by-easy-ai
		Bots: easy
	ProvidesPrerequisite@EasyAI:
		Prerequisite: is-easy-ai
		RequiresCondition: owned-by-easy-ai
	GrantConditionOnBotOwner@NavalAI:
		Condition: owned-by-naval-ai
		Bots: naval
	ProvidesPrerequisite@NavalAI:
		Prerequisite: is-naval-ai
		RequiresCondition: owned-by-naval-ai
	ProductionCostMultiplier@brutalbonus:
		Multiplier: 90
		Prerequisites: is-brutal-ai
	ProductionCostMultiplier@vhardbonus:
		Multiplier: 95
		Prerequisites: is-vhard-ai
	ProductionTimeMultiplier@easybonus:
		Multiplier: 115
		Prerequisites: is-easy-ai

# Refineries have an explicit BuildDuration so build time is unaffected by ProductionCostMultiplier
^BotRefinery:
	ProductionTimeMultiplier@brutalbonus:
		Multiplier: 90
		Prerequisites: is-brutal-ai
	ProductionTimeMultiplier@vhardbonus:
		Multiplier: 95
		Prerequisites: is-vhard-ai

^BotDefenseProductionBonus:
	ProductionCostMultiplier@brutalbonus:
		Multiplier: 85
		Prerequisites: is-brutal-ai
	ProductionCostMultiplier@vhardbonus:
		Multiplier: 90
		Prerequisites: is-vhard-ai
	ProductionCostMultiplier@hardbonus:
		Multiplier: 95
		Prerequisites: is-hard-ai
	ProductionTimeMultiplier@easybonus:
		Multiplier: 115
		Prerequisites: is-easy-ai

^BotGroundProductionBonus:
	ProductionCostMultiplier@brutalbonus:
		Multiplier: 60
		Prerequisites: is-brutal-ai
	ProductionCostMultiplier@vhardbonus:
		Multiplier: 75
		Prerequisites: is-vhard-ai
	ProductionCostMultiplier@hardbonus:
		Multiplier: 90
		Prerequisites: is-hard-ai
	ProductionTimeMultiplier@easybonus:
		Multiplier: 120
		Prerequisites: is-easy-ai

^BotAirProductionBonus:
	ProductionCostMultiplier@brutalbonus:
		Multiplier: 95
		Prerequisites: is-brutal-ai
	ProductionTimeMultiplier@easybonus:
		Multiplier: 120
		Prerequisites: is-easy-ai

# The Bot does not know how to sell up when he does not have an income. This provides an income, when floating less than 2500$
^BotFallbackInsurance:
	GrantConditionOnPlayerResources@botinsurance:
		Condition: noinsurance
		Threshold: 2500
	GrantTimedCondition@botinsurance:
		Condition: noinsurance
		Duration: 3000
	CashTrickler@brutalinsurance:
		Interval: 450
		Amount: 1250
		ShowTicks: False
		RequiresCondition: owned-by-brutal-ai && !noinsurance
	CashTrickler@vhardinsurance:
		Interval: 450
		Amount: 1000
		ShowTicks: False
		RequiresCondition: owned-by-vhard-ai && !noinsurance
	CashTrickler@hardinsurance:
		Interval: 450
		Amount: 750
		ShowTicks: False
		RequiresCondition: owned-by-hard-ai && !noinsurance
	CashTrickler@normalinsurance:
		Interval: 450
		Amount: 500
		ShowTicks: False
		RequiresCondition: owned-by-normal-ai && !noinsurance

#Hacky but helps Bots deal with abandoned vehicles due to snipers
^BotCaptureHelper:
	Armament@AIHELPER:
		Weapon: commandeer
		Name: commandeer
		TargetRelationships: Neutral
		RequiresCondition: botowner
	GrantConditionOnBotOwner@AIHELPER:
		Condition: botowner
		Bots: brutal, vhard, hard, normal, easy, naval
	GrantConditionOnAttack@AIHELPER:
		Condition: removeunit
		ArmamentNames: commandeer
		RequiresCondition: botowner
	KillsSelf@AIHELPER:
		RemoveInstead: true
		RequiresCondition: removeunit
	AutoTargetPriority@AIHELPER:
		ValidTargets: NoCrew
		RequiresCondition: botowner
		Priority: 1
	WithInfantryBody:
		AttackSequences:
			primary: shoot
			commandeer: stand

^InfiltratorStolenTech:
	ProductionCostMultiplier@LegionBonus:
		Multiplier: 90
		Prerequisites: player.legion

^PlayerHandicaps:
	HandicapFirepowerMultiplier:
	HandicapDamageMultiplier:

^GainsExperience:
	GainsExperience:
		LevelUpNotification: LevelUp
		Conditions:
			300: rank-veteran
			600: rank-veteran
			900: rank-veteran
	GrantCondition@RANK-ELITE:
		RequiresCondition: rank-veteran >= 3
		Condition: rank-elite
	DamageMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 95
	DamageMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 90
	DamageMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite && !development-policy3
		Modifier: 75
	FirepowerMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 105
	FirepowerMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 110
	FirepowerMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite && !development-policy3
		Modifier: 130
	ReloadDelayMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 95
	ReloadDelayMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 90
	ReloadDelayMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite && !development-policy3
		Modifier: 80
	ChangesHealth@ELITE:
		PercentageStep: 5
		Delay: 100
		StartIfBelow: 100
		DamageCooldown: 150
		RequiresCondition: rank-elite
	WithDecoration@RANK-1:
		Image: rank
		Sequence: rank-veteran-1
		Palette: effect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-veteran == 1 && !player-nod && !player-scrin
	WithDecoration@RANK-2:
		Image: rank
		Sequence: rank-veteran-2
		Palette: effect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-veteran == 2 && !player-nod && !player-scrin
	WithDecoration@RANK-ELITE:
		Image: rank
		Sequence: rank-elite
		Palette: effect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-elite && !player-nod && !player-scrin
	WithDecoration@NODRANK-1:
		Image: rank
		Sequence: rank-veteran-1-nod
		Palette: effect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-veteran == 1 && player-nod
	WithDecoration@NODRANK-2:
		Image: rank
		Sequence: rank-veteran-2-nod
		Palette: effect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-veteran == 2 && player-nod
	WithDecoration@NODRANK-ELITE:
		Image: rank
		Sequence: rank-elite-nod
		Palette: effect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-elite && player-nod
	WithDecoration@SCRINRANK-1:
		Image: rank
		Sequence: rank-veteran-1-scrin
		Palette: scrineffect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-veteran == 1 && player-scrin
	WithDecoration@SCRINRANK-2:
		Image: rank
		Sequence: rank-veteran-2-scrin
		Palette: scrineffect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-veteran == 2 && player-scrin
	WithDecoration@SCRINRANK-ELITE:
		Image: rank
		Sequence: rank-elite-scrin
		Palette: scrineffect
		Position: BottomRight
		Margin: 5, 2
		ValidRelationships: Ally, Enemy
		RequiresCondition: rank-elite && player-scrin
	GrantConditionOnPrerequisite@NODRANK:
		Condition: player-nod
		Prerequisites: player.nod
	GrantConditionOnPrerequisite@SCRINRANK:
		Condition: player-scrin
		Prerequisites: player.scrin
	GrantConditionOnPrerequisite@DevelopmentPolicy3:
		Condition: development-policy3
		Prerequisites: development.policy, influence.level3
	DamageMultiplier@EliteDevelopmentPolicy3:
		RequiresCondition: rank-elite && development-policy3
		Modifier: 60
	FirepowerMultiplier@EliteDevelopmentPolicy3:
		RequiresCondition: rank-elite && development-policy3
		Modifier: 140
	ReloadDelayMultiplier@EliteDevelopmentPolicy3:
		RequiresCondition: rank-elite && development-policy3
		Modifier: 75

^Berserk:
	SpeedMultiplier@BERSERK:
		RequiresCondition: berserkactive
		Modifier: 125
	ReloadDelayMultiplier@BERSERK:
		RequiresCondition: berserkactive
		Modifier: 50
	ExternalCondition@BERSERK:
		Condition: berserk
	Berserkable@BERSERK:
		RequiresCondition: berserkactive
	WithPalettedOverlay@BERSERK:
		RequiresCondition: berserkactive
		Palette: berserk
	TimedConditionBar@BERSERK:
		Color: ff0066
		Condition: berserk
	RejectsOrders@BERSERK:
		RequiresCondition: berserkactive
	GrantCondition@BERSERK:
		RequiresCondition: berserk
		Condition: berserkactive

^Frenzy:
	SpeedMultiplier@FRENZY:
		RequiresCondition: frenzy
		Modifier: 125
	ReloadDelayMultiplier@FRENZY:
		RequiresCondition: frenzy
		Modifier: 75
	ExternalCondition@FRENZY:
		Condition: frenzyinit
	WithPalettedOverlay@FRENZY:
		RequiresCondition: frenzy
		Palette: frenzy
	TimedConditionBar@FRENZY:
		Color: ff3300
		Condition: frenzy
	WithIdleOverlay@FRENZY:
		Sequence: vehicle
		Image: frenzy-overlay
		Palette: frenzyoverlay
		RequiresCondition: frenzy
		IsDecoration: True
	GrantTimedCondition@FRENZY:
		Condition: frenzy
		RequiresCondition: frenzyinit
		Duration: 500
	ReloadDelayMultiplier@FRENZYDEBUFF:
		RequiresCondition: !frenzy && frenzyinit
		Modifier: 115
	SpeedMultiplier@FRENZYDEBUFF:
		RequiresCondition: !frenzy && frenzyinit
		Modifier: 85

^FrenzyInfantry:
	Inherits: ^Frenzy
	WithIdleOverlay@FRENZY:
		Sequence: infantry

^Inspirable:
	ExternalCondition@InspirationCmsr:
		Condition: cmsr-inspiration
	ExternalCondition@InspirationTrpc:
		Condition: trpc-inspiration
	ExternalCondition@InspirationOvld:
		Condition: ovld-inspiration
	GrantCondition@Inspiration1:
		RequiresCondition: cmsr-inspiration
		Condition: inspiration
	GrantCondition@Inspiration2:
		RequiresCondition: trpc-inspiration
		Condition: inspiration
	GrantCondition@Inspiration3:
		RequiresCondition: ovld-inspiration
		Condition: inspiration
	ReloadDelayMultiplier@Inspiration1:
		RequiresCondition: inspiration == 1
		Modifier: 92
	ReloadAmmoDelayMultiplier@Inspiration1:
		RequiresCondition: inspiration == 1
		Modifier: 92
	SpeedMultiplier@Inspiration1:
		RequiresCondition: inspiration == 1
		Modifier: 130
	ReloadDelayMultiplier@Inspiration2:
		RequiresCondition: inspiration >= 2
		Modifier: 84
	ReloadAmmoDelayMultiplier@Inspiration2:
		RequiresCondition: inspiration >= 2
		Modifier: 84
	SpeedMultiplier@Inspiration2:
		RequiresCondition: inspiration >= 2
		Modifier: 140
	WithIdleOverlay@Inspiration1:
		Image: ussrstar
		Sequence: idle-overlay1
		Palette: effect
		RequiresCondition: inspiration == 1
		IsDecoration: True
	WithIdleOverlay@Inspiration2:
		Image: ussrstar
		Sequence: idle-overlay2
		Palette: effect
		RequiresCondition: inspiration >= 2
		IsDecoration: True

^HeroOfTheUnionTargetable:
	Targetable@HeroOfTheUnion:
		TargetTypes: HeroOfUnionTargetable
		RequiresCondition: !herooftheunion
	ExternalCondition@HeroOfTheUnion:
		Condition: herooftheunion
	DamageMultiplier@HeroOfTheUnion:
		Modifier: 15
		RequiresCondition: herooftheunion
	SpeedMultiplier@HeroOfTheUnion:
		Modifier: 120
		RequiresCondition: herooftheunion
	ReloadDelayMultiplier@HeroOfTheUnion:
		Modifier: 66
		RequiresCondition: herooftheunion
	ReloadAmmoDelayMultiplier@HeroOfTheUnion:
		Modifier: 66
		RequiresCondition: herooftheunion
	RangeMultiplier@HeroOfTheUnion:
		Modifier: 150
		RequiresCondition: herooftheunion
	WithIdleOverlay@HeroOfTheUnionLight:
		Sequence: light
		Image: hero-overlay
		Palette: effect
		RequiresCondition: herooftheunion
		IsDecoration: True
	WithIdleOverlay@HeroOfTheUnionCrest:
		Sequence: star
		Image: hero-overlay
		Palette: effect
		RequiresCondition: herooftheunion
		IsDecoration: True
	TakeCover:
		RequiresCondition: !herooftheunion && !reapersnare
	GrantCondition@HeroElite:
		RequiresCondition: herooftheunion
		Condition: rank-elite

^HeroOfTheUnionTargetableGrenFlamer:
	Inherits: ^HeroOfTheUnionTargetable
	DamageMultiplier@HeroOfTheUnion:
		Modifier: 60
	ReloadDelayMultiplier@HeroOfTheUnion:
		Modifier: 90
	RangeMultiplier@HeroOfTheUnion:
		Modifier: 170

^IdolOfKane:
	ExternalCondition@IdolOfKaneBuff:
		Condition: iokbuff
	SpeedMultiplier@IdolOfKaneBuff:
		RequiresCondition: iokbuff
		Modifier: 130
	ReloadDelayMultiplier@IdolOfKaneBuff:
		RequiresCondition: iokbuff
		Modifier: 85
	ExternalCondition@IdolOfKaneDebuff:
		Condition: iokdebuff
	SpeedMultiplier@IdolOfKaneDebuff:
		RequiresCondition: iokdebuff
		Modifier: 80
	ReloadDelayMultiplier@IdolOfKaneDebuff:
		RequiresCondition: iokdebuff
		Modifier: 120

^Chronoshiftable:
	ChronoshiftableCA:
		Image: chrono
		InitialWarpFromSequence: warpin
		ReturnWarpFromSequence: warpin
		ReturnWarpToSequence: warpout
		RequiresCondition: !being-warped
		Palette: ra2effect-ignore-lighting-alpha75
		ReturnToAvoidDeath: true
		Condition: chronoshifted
	HealthCapDamageMultiplier@CHRONO:
		MaxHealthEquivalent: 47000
		RequiresCondition: chronoshifted

^NoUnloadWhenChronoshifted:
	RejectsOrders@NOUNLOADCHRONO:
		Reject: Unload
		RequiresCondition: chronoshifted

^Warpable:
	Warpable:
		Condition: being-warped
		RevokeDelay: 25
		RevokeRate: 250
		DamageTypes: ChronoDeath
		ScaleWithCurrentHealthPercentage: true
		ShowSelectionBar: true
		SelectionBarColor: b6f4ff
	Targetable@TEMPORAL:
		TargetTypes: Temporal
	TimedConditionBar@TEMPORAL:
		Color: b6f4ff
		Condition: being-warped
	WithPalettedOverlay@TEMPORALl:
		RequiresCondition: being-warped
		Palette: temporal
	WithIdleOverlay@TEMPORAL:
		Sequence: chrono-overlay
		RequiresCondition: being-warped
		IsDecoration: True
	FireWarheadsOnDeath@TEMPORAL:
		Weapon: TemporalExplode
		EmptyWeapon: TemporalExplode
		DeathTypes: ChronoDeath
	ExternalCondition@TEMPORAL:
		Condition: being-warped
	SpeedMultiplier@TEMPORAL:
		Modifier: 0
		RequiresCondition: being-warped
	RejectsOrders@TEMPORAL:
		RequiresCondition: being-warped

^WarpableBuilding:
	Inherits@1: ^Warpable
	PowerMultiplier@TEMPORAL:
		RequiresCondition: being-warped
		Modifier: 0

^TemporalReinforcement:
	GrantTimedCondition@TEMPINC:
		Condition: warpin
		Duration: 1000
	KillsSelf@TEMPINC:
		GrantsCondition: warpout
		RequiresCondition: !warpin
	FireWarheadsOnDeath@TEMPINC:
		Weapon: UnitExplodeWarpOut
		EmptyWeapon: UnitExplodeWarpOut
		RequiresCondition: warpout
	TimedConditionBar@TEMPINC:
		Condition: warpin
		Color: FFFFFF

^Blindable:
	ExternalCondition@Blinded:
		Condition: blinded
	RevealsShroudMultiplier@Blinded:
		RequiresCondition: blinded
		Modifier: 0
	TimedConditionBar@Blinded:
		Condition: blinded
		Color: ffee00
	WithDecoration@Blinded:
		Image: blinded
		Sequence: idle
		Palette: effect
		Position: Top
		ValidRelationships: Ally, Enemy, Neutral
		Margin: 0, 8
		RequiresCondition: blinded

^Suppressable:
	SpeedMultiplier@SUPPRESSION:
		RequiresCondition: suppression
		Modifier: 40
	ReloadDelayMultiplier@SUPPRESSION:
		RequiresCondition: suppression
		Modifier: 125
	ExternalCondition@SUPPRESSION:
		Condition: suppression
	WithPalettedOverlay@SUPPRESSION:
		RequiresCondition: suppression
		Palette: suppression
	TimedConditionBar@SUPPRESSION:
		Condition: suppression
		Color: f454d0

^Atomizable:
	ExternalCondition@ATOMIZED:
		Condition: atomized
	TimedConditionBar@ATOMIZED:
		Condition: atomized
		Color: ff0000
	WithIdleOverlay@ATOMIZED:
		Sequence: vehicle
		Image: atomize-overlay
		Palette: scrin
		RequiresCondition: atomized
		IsDecoration: True
	SpeedMultiplier@ATOMIZED:
		RequiresCondition: atomized
		Modifier: 66
	FirepowerMultiplier@ATOMIZED:
		RequiresCondition: atomized
		Modifier: 66

^AtomizableInfantry:
	Inherits: ^Atomizable
	WithIdleOverlay@ATOMIZED:
		Sequence: infantry

^IonSurgable:
	TimedConditionBar@IONSURGE:
		Condition: ionsurge
		Color: b070ff
	SpeedMultiplier@IONSURGE:
		RequiresCondition: ionsurge
		Modifier: 150
	ExternalCondition@IONSURGE:
		Condition: ionsurge
	WithColoredOverlay@IONSURGE:
		RequiresCondition: ionsurge
		Color: bd70ff23
	WithIdleOverlay@IONSURGE:
		Sequence: vehicle
		Image: ionsurge-overlay
		Palette: ionsurgeoverlay
		RequiresCondition: ionsurge
		IsDecoration: True
	Targetable@IONSURGE:
		TargetTypes: IonSurgable
		RequiresCondition: !ionsurge

^IonSurgableInfantry:
	Inherits: ^IonSurgable
	WithIdleOverlay@IONSURGE:
		Sequence: infantry

^PowerDrainable:
	Targetable@POWERDRAIN:
		TargetTypes: PowerDrainable
	ExternalCondition@POWERDRAIN:
		Condition: powerdrain
	PowerMultiplier@POWERDRAIN:
		Modifier: 0
		RequiresCondition: powerdrain
	ExternalCondition@DiscPowerDrain:
		Condition: discpowerdrain
	Power@DiscPowerDrain:
		Amount: -10000
		RequiresCondition: discpowerdrain
	WithColoredOverlay@POWERDRAIN:
		RequiresCondition: powerdrain || discpowerdrain
		Color: 000000b4
	WithDecoration@POWERDRAIN:
		Image: poweroff
		Sequence: offline
		Palette: chrome
		RequiresCondition: powerdrain || discpowerdrain
		Position: Center

^ResourceDrainable:
	Targetable@ResourceDrain:
		TargetTypes: ResourceDrainable
	ExternalCondition@ResourceDrain:
		Condition: resourcedrain
	CashTrickler@ResourceDrain:
		Interval: 50
		Amount: -30
		RequiresCondition: resourcedrain
	WithColoredOverlay@ResourceDrain:
		RequiresCondition: resourcedrain
		Color: ffff0022

^UpgradeOverlay:
	WithDecoration@UpgradeOverlay:
		Image: upgrade
		Palette: chrome
		Sequence: upgrade
		Position: Center
		RequiresCondition: upgrading
		BlinkInterval: 5
		BlinkPattern: on
		ValidRelationships: Ally, Neutral

^GDIUpgrade:
	GrantConditionOnPrerequisite@HOLD:
		Condition: hold
		Prerequisites: hold.strat
	GrantConditionOnPrerequisite@HOLD2:
		Condition: hold2
		Prerequisites: hold2.strat
	GrantConditionOnPrerequisite@HOLD3:
		Condition: hold3
		Prerequisites: hold3.strat
	GrantConditionOnPrerequisite@BOMBARD:
		Condition: bombard
		Prerequisites: bombard.strat
	GrantConditionOnPrerequisite@BOMBARD2:
		Condition: bombard2
		Prerequisites: bombard2.strat
	GrantConditionOnPrerequisite@BOMBARD3:
		Condition: bombard3
		Prerequisites: bombard3.strat
	GrantConditionOnPrerequisite@SEEK:
		Condition: seek
		Prerequisites: seek.strat
	GrantConditionOnPrerequisite@SEEK2:
		Condition: seek2
		Prerequisites: seek2.strat
	GrantConditionOnPrerequisite@SEEK3:
		Condition: seek3
		Prerequisites: seek3.strat
	DamageMultiplier@HOLD:
		RequiresCondition: hold
		Modifier: 95
	DamageMultiplier@HOLD2:
		RequiresCondition: hold2
		Modifier: 95
	DamageMultiplier@HOLD3:
		RequiresCondition: hold3
		Modifier: 95
	FirepowerMultiplier@BOMBARD:
		RequiresCondition: bombard
		Modifier: 103
	FirepowerMultiplier@BOMBARD2:
		RequiresCondition: bombard2
		Modifier: 103
	FirepowerMultiplier@BOMBARD3:
		RequiresCondition: bombard3
		Modifier: 103
	ReloadDelayMultiplier@BOMBARD:
		RequiresCondition: bombard
		Modifier: 97
	ReloadDelayMultiplier@BOMBARD2:
		RequiresCondition: bombard2
		Modifier: 97
	ReloadDelayMultiplier@BOMBARD3:
		RequiresCondition: bombard3
		Modifier: 97
	RangeMultiplier@SEEK:
		RequiresCondition: seek
		Modifier: 105
	SpeedMultiplier@SEEK:
		RequiresCondition: seek
		Modifier: 105
	RangeMultiplier@SEEK2:
		RequiresCondition: seek2
		Modifier: 105
	SpeedMultiplier@SEEK2:
		RequiresCondition: seek2
		Modifier: 105
	RangeMultiplier@SEEK3:
		RequiresCondition: seek3
		Modifier: 105
	SpeedMultiplier@SEEK3:
		RequiresCondition: seek3
		Modifier: 105
	WithDecoration@HOLD:
		Image: pips
		Sequence: pip-armor
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: hold && !(hold2 || hold3)
		BlinkInterval: 64
		BlinkPatterns:
			hold && bino: Off, On
		RequiresSelection: True
	WithDecoration@HOLD2:
		Image: pips
		Sequence: pip-armor2
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: hold2 && !hold3
		BlinkInterval: 64
		BlinkPatterns:
			hold2 && bino: Off, On
		RequiresSelection: True
	WithDecoration@HOLD3:
		Image: pips
		Sequence: pip-armor3
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: hold3
		BlinkInterval: 64
		BlinkPatterns:
			hold3 && bino: Off, On
		RequiresSelection: True
	WithDecoration@BOMBARD:
		Image: pips
		Sequence: pip-bombard
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: bombard && !(bombard2 || bombard3)
		BlinkInterval: 64
		BlinkPatterns:
			bombard && bino: Off, On
		RequiresSelection: True
	WithDecoration@BOMBARD2:
		Image: pips
		Sequence: pip-bombard2
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: bombard2 && !bombard3
		BlinkInterval: 64
		BlinkPatterns:
			bombard2 && bino: Off, On
		RequiresSelection: True
	WithDecoration@BOMBARD3:
		Image: pips
		Sequence: pip-bombard3
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: bombard3
		BlinkInterval: 64
		BlinkPatterns:
			bombard3 && bino: Off, On
		RequiresSelection: True
	WithDecoration@SEEK:
		Image: pips
		Sequence: pip-seek
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: seek && !(seek2 || seek3)
		BlinkInterval: 64
		BlinkPatterns:
			seek && bino: Off, On
		RequiresSelection: True
	WithDecoration@SEEK2:
		Image: pips
		Sequence: pip-seek2
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: seek2 && !seek3
		BlinkInterval: 64
		BlinkPatterns:
			seek2 && bino: Off, On
		RequiresSelection: True
	WithDecoration@SEEK3:
		Image: pips
		Sequence: pip-seek3
		Palette: effect
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: seek3
		BlinkInterval: 64
		BlinkPatterns:
			seek3 && bino: Off, On
		RequiresSelection: True

^GDIUpgradeAircraft:
	Inherits: ^GDIUpgrade
	WithDecoration@HOLD:
		-BlinkPatterns:
		BlinkPatterns:
			hold: Off, On
	WithDecoration@HOLD2:
		-BlinkPatterns:
		BlinkPatterns:
			hold2: Off, On
	WithDecoration@HOLD3:
		-BlinkPatterns:
		BlinkPatterns:
			hold3: Off, On
	WithDecoration@BOMBARD:
		-BlinkPatterns:
		BlinkPatterns:
			bombard: Off, On
	WithDecoration@BOMBARD2:
		-BlinkPatterns:
		BlinkPatterns:
			bombard2: Off, On
	WithDecoration@BOMBARD3:
		-BlinkPatterns:
		BlinkPatterns:
			bombard3: Off, On
	WithDecoration@SEEK:
		-BlinkPatterns:
		BlinkPatterns:
			seek: Off, On
	WithDecoration@SEEK2:
		-BlinkPatterns:
		BlinkPatterns:
			seek2: Off, On
	WithDecoration@SEEK3:
		-BlinkPatterns:
		BlinkPatterns:
			seek3: Off, On

^HazmatUpgrade:
	GrantConditionOnPrerequisite@HAZMAT:
		Condition: hazmat-upgrade
		Prerequisites: hazmat.upgrade
	GrantConditionOnPrerequisite@HAZMATSOVIET:
		Condition: hazmatsoviet-upgrade
		Prerequisites: hazmatsoviet.upgrade
	GrantConditionOnPrerequisite@HAZMATZOCOM:
		Condition: hazmat-upgrade
		Prerequisites: player.zocom
	GrantCondition@HAZMAT:
		Condition: hazmatsuits
		RequiresCondition: hazmat-upgrade && !hazmatsoviet-upgrade
	GrantCondition@HAZMATSOVIET:
		Condition: hazmatsuits-soviet
		RequiresCondition: hazmatsoviet-upgrade
	WithDecoration@HAZMAT:
		Image: pips
		Sequence: pip-hazmat
		Palette: chrometd
		Position: BottomLeft
		RequiresCondition: hazmatsuits
		RequiresSelection: True
		BlinkInterval: 64
		BlinkPatterns:
			bino: Off, On
	WithDecoration@HAZMATSOVIET:
		Image: pips
		Sequence: pip-hazmats
		Palette: chrome
		Position: BottomLeft
		RequiresCondition: hazmatsuits-soviet
		RequiresSelection: True
		BlinkInterval: 64
		BlinkPatterns:
			bino: Off, On
	GrantConditionOnTerrain@HAZMATSOVIET:
		TerrainTypes: Rough, Beach, Ford, Ore, Gems, Tiberium, BlueTiberium, BlackTiberium
		Condition: onrough
	SpeedMultiplier@HAZMATSOVIET:
		RequiresCondition: hazmatsuits-soviet && onrough
		Modifier: 66
	Targetable@HAZMAT:
		TargetTypes: Hazmat
		RequiresCondition: hazmatsuits-soviet || hazmatsuits

^NodCyborgUpgrade:
	GrantConditionOnPrerequisite@CYBORGARMOR:
		Condition: cyborgarmor-upgrade
		Prerequisites: cyborgarmor.upgrade
	GrantConditionOnPrerequisite@CYBORGDAMAGE:
		Condition: cyborgdmg-upgrade
		Prerequisites: cyborgdmg.upgrade
	GrantConditionOnPrerequisite@CYBORGSPEED:
		Condition: cyborgspeed-upgrade
		Prerequisites: cyborgspeed.upgrade
	DamageMultiplier@CYBORGARMOR:
		RequiresCondition: cyborgarmor-upgrade
		Modifier: 80
	FirepowerMultiplier@CYBORGDAMAGE:
		RequiresCondition: cyborgdmg-upgrade
		Modifier: 110
	ProductionTimeMultiplier@CYBORGPROD:
		Multiplier: 90
		Prerequisites: cyborgprod.upgrade
	SpeedMultiplier@CYBORGSPEED:
		RequiresCondition: cyborgspeed-upgrade
		Modifier: 120
	DamageTypeDamageMultiplier@CYBORGFLAKARMOR:
		RequiresCondition: cyborgarmor-upgrade
		DamageTypes: FlakVestMitigated
		Modifier: 85
	DamageTypeDamageMultiplier@CYBORGFLAKARMORMINOR:
		RequiresCondition: cyborgarmor-upgrade
		DamageTypes: FlakVestMitigatedMinor
		Modifier: 95

^BinoUpgrade:
	ExternalCondition@BINO:
		Condition: bino
	RevealsShroudMultiplier@BINO:
		Modifier: 125
		RequiresCondition: bino
	InaccuracyMultiplier@BINO:
		Modifier: 50
		RequiresCondition: bino
	WithDecoration@BINO:
		Image: pips
		Sequence: pip-bino
		Palette: chrome
		Position: BottomLeft
		RequiresCondition: bino
		BlinkInterval: 64
		BlinkPatterns:
			bino && hold: On, Off
			bino && seek: On, Off
			bino && bombard: On, Off
		RequiresSelection: True

^BinoUpgradeInfantry:
	Inherits: ^BinoUpgrade
	WithDecoration@BINO:
		-BlinkPatterns:
		BlinkPatterns:
			hazmatsuits: On, Off
			hazmatsuits-soviet: On, Off

^AuraHealable:
	ChangesHealth@HOSPITAL:
		Step: 500
		Delay: 75
		StartIfBelow: 100
		DamageCooldown: 100
		RequiresCondition: hospitalheal
	ExternalCondition@HOSPITAL:
		Condition: hospitalheal
	WithDecoration@REDCROSS:
		Image: pips
		Sequence: medic
		Palette: chrome
		Position: BottomRight
		RequiresCondition: hospitalheal && damaged
		BlinkInterval: 32
		Margin: -1, 7
		BlinkPattern: Off, On
	WithDecoration@RANK-1:
		BlinkInterval: 32
		BlinkPatterns:
			damaged && hospitalheal: On, Off
	WithDecoration@RANK-2:
		BlinkInterval: 32
		BlinkPatterns:
			damaged && hospitalheal: On, Off
	WithDecoration@RANK-ELITE:
		BlinkInterval: 32
		BlinkPatterns:
			damaged && hospitalheal: On, Off

^AuraRepairable:
	ChangesHealth@MACS:
		Step: 500
		Delay: 75
		StartIfBelow: 100
		DamageCooldown: 100
		RequiresCondition: macsrepair
	ExternalCondition@MACS:
		Condition: macsrepair
	WithDecoration@MACSREPAIR:
		Image: select
		Palette: chrome
		Sequence: repair-small
		Position: Center
		RequiresCondition: macsrepair && damaged
		BlinkInterval: 32
		BlinkPattern: on, off

^LeecherHealable:
	ExternalCondition@LCHRHEAL:
		Condition: lchr-healing
	WithIdleOverlay@LCHRHEAL:
		Image: lchrheal
		Sequence: vehicle
		Palette: scrin
		RequiresCondition: lchr-healing && damaged
		Offset: 0,0,400

^HealingCooldown:
	GrantConditionOnHealingReceived@HEALINGCOOLDOWN:
		Condition: heal-cooldown
		RequiredHealing: 50000
		StackDuration: 1125
		MinimumHealing: 2500
		DamageTypes: DirectHeal
		ShowSelectionBar: true
	Targetable@HEAL:
		RequiresCondition: !parachute && damaged && !being-warped && !heal-cooldown

^IronCurtainable:
	WithPalettedOverlay@IRONCURTAIN:
		RequiresCondition: invulnerability
		Palette: invuln
	DamageMultiplier@IRONCURTAIN:
		RequiresCondition: invulnerability
		Modifier: 0
	TimedConditionBar@IRONCURTAIN:
		Condition: invulnerability
	ExternalCondition@INVULNERABILITY:
		Condition: invulnerability
	SpeedCapSpeedMultiplier@IRONCURTAIN:
		RequiresCondition: invulnerability
		MaxSpeed: 60

^IronCurtainable-Kills:
	Inherits: ^IronCurtainable
	KillsSelf@Immolate:
		DamageTypes: FireDeath
		RequiresCondition: invulnerability

^TankBusterVulnerability:
	DamageTypeDamageMultiplier@TANKBUSTERVULN:
		DamageTypes: TankBuster
		Modifier: 133

^AirToGroundProtection:
	DamageTypeDamageMultiplier@A2GPROTECTION:
		DamageTypes: AirToGround
		Modifier: 50

^GyroStabilizers:
	WithPalettedOverlay@GYRO:
		RequiresCondition: gyrostabilizers
		Palette: gyro
	RangeMultiplier@GYRO:
		RequiresCondition: gyrostabilizers
		Modifier: 133
	ReloadDelayMultiplier@GYRO:
		RequiresCondition: gyrostabilizers
		Modifier: 133
	TimedConditionBar@GYRO:
		Condition: gyrostabilizers
	ExternalCondition@GYRO:
		Condition: gyrostabilizers
	GrantTimedConditionOnDeploy@GYRO:
		DeployedCondition: gyrostabilizers
		CooldownTicks: 1500
		DeployedTicks: 500
		StartsFullyCharged: true
		ChargingColor: 808000
		DischargingColor: ffff00
		ShowSelectionBarWhenFull: false
		DeploySound: gyrostabilizers.aud
		RequiresCondition: gyro-upgrade
		Instant: true
	GrantConditionOnPrerequisite@GYRO:
		Condition: gyro-upgrade
		Prerequisites: gyro.upgrade
	WithFlashEffect@GYRO:
		Color: ffffff
		Interval: 5000
		RequiresCondition: gyrostabilizers

^Irradiatable:
	WithPalettedOverlay@IRRADIATABLE:
		RequiresCondition: irradiated
		Palette: irrad
	TimedConditionBar@IRRADIATABLE:
		Condition: irradiated
		Color: 88DD00
	ExternalCondition@IRRADIATED:
		Condition: irradiated
	PeriodicExplosion@IRRADIATED:
		Weapon: IrradiatedUnit
		RequiresCondition: irradiated
	FirepowerMultiplier@IRRADIATED:
		RequiresCondition: irradiated
		Modifier: 90
	DamageMultiplier@IRRADIATED:
		Modifier: 110
		RequiresCondition: irradiated

^AtomicAmmunition:
	ExternalCondition@ATOMICAMMO:
		Condition: atomic-ammo
	WithRestartableIdleOverlay@ATOMICAMMO:
		Sequence: idle
		Image: atomshell
		Palette: effect
		RequiresCondition: atomic-ammo && has-atomic-ammo < 12
		Offset: 0,0,600
		IsDecoration: True
		PlayOnce: True
	FirepowerMultiplier@ATOMICAMMO:
		RequiresCondition: has-atomic-ammo
		Modifier: 180
	Targetable@ATOMICAMMO:
		TargetTypes: AtomicAmmoTargetable
	AmmoPool@ATOMICAMMO:
		Armaments: primary
		Ammo: 12
		InitialAmmo: 0
		AmmoCondition: has-atomic-ammo
	WithAmmoPipsDecoration@ATOMICAMMO:
		Position: BottomLeft
		Margin: 4, 3
		PipCount: 6
		ValidRelationships: Ally, Enemy, Neutral
		RequiresSelection: false
		RequiresCondition: has-atomic-ammo
		AmmoPools: primary
	ReloadAmmoPool@ATOMICAMMO:
		Count: 12
		Delay: 1
		RequiresCondition: atomic-ammo && has-atomic-ammo < 12
	ReloadAmmoPoolCA@ATOMICAMMODECAY:
		Count: -12
		Delay: 3000
		RequiresCondition: has-atomic-ammo
		ShowSelectionBar: false

^ForceShieldable:
	WithPalettedOverlay@FS:
		RequiresCondition: forceshield && !invulnerability
		Palette: forces
	DamageMultiplier@FS:
		RequiresCondition: forceshield
		Modifier: 0
	TimedConditionBar@FS:
		Condition: forceshield
		Color: 2f74e2
	ExternalCondition@FS:
		Condition: forceshield
	PowerMultiplier@FSPOWERDOWN:
		RequiresCondition: forceshield && !invulnerability
		Modifier: 0

^EmpDisable:
	WithColoredOverlay@EMPDISABLE:
		RequiresCondition: empdisable
		Color: 000000b4
	TimedConditionBar@EMPDISABLE:
		Condition: empdisable
		Color: FFFFFF
	WithIdleOverlay@EMPDISABLE:
		Sequence: emp-overlay
		Palette: tseffect
		RequiresCondition: empdisable
		IsDecoration: True
	PowerMultiplier@EMPDISABLE:
		RequiresCondition: empdisable
		Modifier: 0
	ExternalCondition@EMPDISABLE:
		Condition: empdisable

^EmpVisualEffect:
	WithColoredOverlay@EMPDISABLE:
		Color: 000000b4
	WithIdleOverlay@EMPDISABLE:
		Sequence: emp-overlay
		Palette: tseffect
		IsDecoration: True

^NaniteRepair:
	ChangesHealth@NREPAIR:
		PercentageStep: 5
		Delay: 25
		StartIfBelow: 100
		DamageCooldown: 0
		RequiresCondition: nrepair
	WithPalettedOverlay@NREPAIR:
		RequiresCondition: nrepair && damaged && !(empdisable || invulnerability || invisibility)
		Palette: nrepair
	TimedConditionBar@NREPAIR:
		Condition: nrepair
		Color: 25cde999
	WithIdleOverlay@NREPAIR:
		Image: nrepair-overlay
		Sequence: idle
		Palette: effect
		RequiresCondition: nrepair && damaged
		IsDecoration: True
	WithDecoration@NREPAIR:
		Image: select
		Palette: chrome
		Sequence: repair-small
		Position: Center
		RequiresCondition: nrepair
		BlinkInterval: 15
		BlinkPattern: on, off
		RequiresSelection: True
		ValidRelationships: Ally, Neutral
	ExternalCondition@NREPAIR-AI:
		Condition: nrepair
	WithIdleOverlay@NSHIELD:
		Sequence: idle
		Image: nshield-overlay
		Palette: effect
		RequiresCondition: nshield && !(empdisable || invulnerability || invisibility)
		IsDecoration: True
	ExternalCondition@NSHIELD:
		Condition: nshield
	DamageMultiplier@NSHIELD:
		RequiresCondition: nshield
		Modifier: 50

^RepairsAircraftWithRepairBay:
	ExternalCondition@AIRCRAFTREPAIR:
		Condition: aircraft-repair
	RepairsUnits:
		ValuePercentage: 0
		HpPerStep: 1000
		Interval: 7
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: Repairing.
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: Unit repaired.
		RequiresCondition: aircraft-repair && !forceshield && !invulnerability && !being-warped
	WithDecoration@AIRCRAFTREPAIR:
		Image: select
		Palette: chrome
		Sequence: repair-small
		Position: Bottom
		BlinkInterval: 15
		BlinkPattern: on, off
		RequiresCondition: aircraft-repair
		RequiresSelection: True
		ValidRelationships: Ally, Neutral

^AffectedByDriverKill:
	GrantConditionIfOwnerIsNeutral@DRIVER_DEAD:
		Condition: driver-dead
	Capturable@DRIVER_DEAD:
		Types: driverless_vehicle
		RequiresCondition: driver-dead
		ValidRelationships: Neutral
		CancelActivity: true
	ChangesHealth@DRIVER_DEAD:
		PercentageStep: -1
		Delay: 50
		StartIfBelow: 101
		DamageCooldown: 0
		RequiresCondition: driver-dead
	CaptureManager:
	Targetable@DRIVERKILL:
		TargetTypes: DriverKill
		RequiresCondition: !driver-dead && !invulnerability
	Targetable@DRIVERKILLLOWHP:
		TargetTypes: DriverKillLow
		RequiresCondition: !driver-dead && hp-below-50-perc && !invulnerability
	GrantConditionOnDamageState@DriverKillLessThan50:
		Condition: hp-below-50-perc
		ValidDamageStates: Heavy, Critical
	Targetable@AICAPTURE:
		TargetTypes: NoCrew
		RequiresCondition: driver-dead
	TooltipDescription@DRIVER_DEAD:
		Description: Crewless. Capturable by infantry.
		RequiresCondition: driver-dead

^CanCaptureDriverlessVehicles:
	Captures@DRIVER_KILL:
		CaptureTypes: driverless_vehicle
	CaptureManager:

^Cloakable:
	ExternalCondition@INVIS:
		Condition: invisibility
	GrantCondition@INVIS:
		Condition: invisibility
		RequiresCondition: tibstealth || sgencloak
	TimedConditionBar@TIBSTEALTH:
		Condition: tibstealth
		Color: 66dd00
	Cloak@TIBSTEALTH:
		RequiresCondition: tibstealth && !cloak-force-disabled
		InitialDelay: 0
		CloakDelay: 25
		IsPlayerPalette: false
		CloakStyle: Palette
		CloakedPalette: cloakgreen
		CloakSound: cloak5md.aud
		UncloakSound: appear1md.aud
		UncloakOn: Attack, Unload, Infiltrate, Demolish
	DamageMultiplier@TIBSTEALTH:
		Modifier: 90
		RequiresCondition: tibstealth
	ExternalCondition@TIBSTEALTH:
		Condition: tibstealth
	Cloak@SGENCLOAK:
		RequiresCondition: sgencloak && !cloak-force-disabled
		PauseOnCondition: tibstealth
		InitialDelay: 0
		CloakDelay: 150
		IsPlayerPalette: false
		CloakStyle: Palette
		CloakedPalette: cloak
		CloakSound: cloak5md.aud
		UncloakSound: appear1md.aud
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Damage, Heal
	ExternalCondition@SGENCLOAK:
		Condition: sgencloak
	ExternalCondition@CloakForceDisabled:
		Condition: cloak-force-disabled
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility
		ColorSource: Player

^CrateCloak:
	Cloak@CRATE-CLOAK:
		InitialDelay: 90
		CloakDelay: 90
		CloakSound: trans1.aud
		UncloakSound: appear1md.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		RequiresCondition: (crate-cloak) && !(cloak-force-disabled || invisibility)
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Damage, Heal
	TimedConditionBar@CRATE-CLOAK:
		Condition: crate-cloak
		Color: 606060
	ExternalCondition@CRATE-CLOAK:
		Condition: crate-cloak
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Heavy, Critical

^Chillable:
	TimedConditionBar@chilled:
		Condition: chilled
		Color: 72edff
	ExternalCondition@chilled:
		Condition: chilled
	SpeedMultiplier@chilled1:
		Modifier: 75
		RequiresCondition: chilled == 1
	DamageMultiplier@chilled1:
		Modifier: 110
		RequiresCondition: chilled == 1
	WithColoredOverlay@chilled1:
		Color: 51aeda20
		RequiresCondition: chilled == 1
	SpeedMultiplier@chilled2:
		Modifier: 65
		RequiresCondition: chilled == 2
	DamageMultiplier@chilled2:
		Modifier: 120
		RequiresCondition: chilled == 2
	WithColoredOverlay@chilled2:
		Color: 51aeda32
		RequiresCondition: chilled == 2
	SpeedMultiplier@chilled3:
		Modifier: 57
		RequiresCondition: chilled == 3
	DamageMultiplier@chilled3:
		Modifier: 130
		RequiresCondition: chilled == 3
	WithColoredOverlay@chilled3:
		Color: 51aeda44
		RequiresCondition: chilled == 3
	SpeedMultiplier@chilled4:
		Modifier: 50
		RequiresCondition: chilled == 4
	DamageMultiplier@chilled4:
		Modifier: 140
		RequiresCondition: chilled == 4
	WithColoredOverlay@chilled4:
		Color: 51aeda56
		RequiresCondition: chilled == 4
	SpeedMultiplier@chilled5:
		Modifier: 44
		RequiresCondition: chilled == 5
	DamageMultiplier@chilled5:
		Modifier: 150
		RequiresCondition: chilled == 5
	WithColoredOverlay@chilled5:
		Color: 51aeda68
		RequiresCondition: chilled == 5
	SpeedMultiplier@chilled6:
		Modifier: 41
		RequiresCondition: chilled == 6
	DamageMultiplier@chilled6:
		Modifier: 160
		RequiresCondition: chilled == 6
	WithColoredOverlay@chilled6:
		Color: 51aeda7a
		RequiresCondition: chilled == 6
	SpeedMultiplier@chilled7:
		Modifier: 38
		RequiresCondition: chilled == 7
	DamageMultiplier@chilled7:
		Modifier: 170
		RequiresCondition: chilled == 7
	WithColoredOverlay@chilled7:
		Color: 51aeda8c
		RequiresCondition: chilled == 7
	SpeedMultiplier@chilled8:
		Modifier: 35
		RequiresCondition: chilled == 8
	DamageMultiplier@chilled8:
		Modifier: 180
		RequiresCondition: chilled == 8
	WithColoredOverlay@chilled8:
		Color: 51aeda9e
		RequiresCondition: chilled == 8
	SpeedMultiplier@chilled9:
		Modifier: 32
		RequiresCondition: chilled == 9
	DamageMultiplier@chilled9:
		Modifier: 190
		RequiresCondition: chilled == 9
	WithColoredOverlay@chilled9:
		Color: 51aedab0
		RequiresCondition: chilled == 9
	SpeedMultiplier@chilled10:
		Modifier: 30
		RequiresCondition: chilled >= 10
	DamageMultiplier@chilled10:
		Modifier: 200
		RequiresCondition: chilled >= 10
	WithColoredOverlay@chilled10:
		Color: 51aedac2
		RequiresCondition: chilled >= 10

^Slowable:
	ExternalCondition@slowed:
		Condition: slowed
	SpeedMultiplier@slowed1:
		Modifier: 75
		RequiresCondition: slowed == 1
	SpeedMultiplier@slowed2:
		Modifier: 50
		RequiresCondition: slowed > 1

^Concussion:
	TimedConditionBar@concussion:
		Condition: concussion
		Color: ffffff
	ExternalCondition@concussion:
		Condition: concussion
	SpeedMultiplier@concussion:
		Modifier: 75
		RequiresCondition: concussion
	ReloadDelayMultiplier@concussion:
		Modifier: 200
		RequiresCondition: concussion
	WithDecoration@Concussion:
		Image: pips
		Sequence: pip-conc
		Palette: chrome
		Position: TopCenter
		RequiresCondition: concussion
		BlinkInterval: 16
		BlinkPatterns:
			concussion: On, On, Off
		RequiresSelection: False
		ValidRelationships: Ally, Neutral, Enemy

^ReaperSnareable:
	WithIdleOverlay@ReaperSnare:
		Sequence: infantry
		Image: reap-snareoverlay
		Palette: effect
		RequiresCondition: reapersnare
		IsDecoration: True
	ExternalCondition@ReaperSnare:
		Condition: reapersnare
	SpeedMultiplier@ReaperSnare:
		Modifier: 0
		RequiresCondition: reapersnare

^HarvesterBalancer:
	GrantConditionOnPrerequisite@HARVBALANCE:
		Condition: global-balancedharvesting
		Prerequisites: global.balancedharvesting
	SpeedMultiplier@HARVBALANCE:
		RequiresCondition: harv-balance
		Modifier: 138
	HarvesterBalancer:
		Condition: harv-balance
		RequiresCondition: global-balancedharvesting

^HarvesterDamageNotification:
	NotificationOnDamage:
		Type: Harvester
		Notification: HarvesterAttack
		TextNotification: Harvester under attack.
		MinimumDamage: 500

^Veilable:
	RangeMultiplier@VEILED:
		Modifier: 80
		RequiresCondition: veiled && !gapveiled
	RangeMultiplier@GAPVEILED:
		Modifier: 60
		RequiresCondition: gapveiled
	RevealsShroudMultiplier@GAPVEILED:
		Modifier: 80
		RequiresCondition: gapveiled
	ExternalCondition@VEILED:
		Condition: veiled
	ExternalCondition@GAPVEILED:
		Condition: gapveiled
	WithIdleOverlay@VEILED:
		Sequence: vehicle
		Image: veildebuff
		Palette: effect-ignore-lighting-alpha35
		RequiresCondition: veiled || gapveiled
		IsDecoration: True

^VeilableInfantry:
	Inherits: ^Veilable
	WithIdleOverlay@VEILED:
		Sequence: infantry

^WeaponJammable:
	ReloadDelayMultiplier@WEAPJAMMED:
		RequiresCondition: weapjammed
		Modifier: 120
	FirepowerMultiplier@WEAPJAMMED:
		RequiresCondition: weapjammed
		Modifier: 90
	ExternalCondition@WEAPJAMMED:
		Condition: weapjammed
	WithIdleOverlay@WEAPJAMMED:
		Sequence: idle
		Image: jamdebuff
		RequiresCondition: weapjammed
		IsDecoration: True

^KillZoneTargetable:
	DamageMultiplier@KillZone:
		Modifier: 125
		RequiresCondition: killzone
	ExternalCondition@KillZone:
		Condition: killzone

^ZoneDefenderShieldable:
	ExternalCondition@ZoneDefenderShield:
		Condition: zdef-shield
	WithColoredOverlay@ZoneDefenderShield:
		Color: 00ffff10
		RequiresCondition: zdef-shield
	DamageMultiplier@ZoneDefenderShield1:
		Modifier: 75
		RequiresCondition: zdef-shield == 1
	DamageMultiplier@ZoneDefenderShield2:
		Modifier: 70
		RequiresCondition: zdef-shield == 2
	DamageMultiplier@ZoneDefenderShield3:
		Modifier: 65
		RequiresCondition: zdef-shield == 3
	DamageMultiplier@ZoneDefenderShield4:
		Modifier: 60
		RequiresCondition: zdef-shield == 4
	DamageMultiplier@ZoneDefenderShield5:
		Modifier: 55
		RequiresCondition: zdef-shield == 5
	DamageMultiplier@ZoneDefenderShield6:
		Modifier: 50
		RequiresCondition: zdef-shield >= 6
	WithIdleOverlay@ZoneDefenderShield:
		Sequence: idle
		Image: zdefshield-overlay
		Palette: tdeffect
		RequiresCondition: zdef-shield
		IsDecoration: True

^C4Plantable:
	DelayedWeaponAttachable@C4:
		Type: c4
		Condition: c4
		ProgressBarColor: FF000080
		RequiresCondition: !invulnerability
	DelayedWeaponAttachable@C4Seal:
		Type: c4seal
		Condition: c4seal
		ProgressBarColor: FF000080
		RequiresCondition: !invulnerability
	Targetable@C4Plantable:
		TargetTypes: C4Plantable
	Targetable@C4Attached:
		TargetTypes: C4Attached
		RequiresCondition: c4 || c4seal
	WithDecoration@c4:
		Image: c4
		Sequence: c4
		Position: CenterCenter
		RequiresCondition: c4 || c4seal
		RequiresSelection: False
		ValidRelationships: Ally, Neutral, Enemy
	AmbientSound@c4:
		SoundFiles: icoltima.aud, icoltimb.aud, icoltimc.aud
		Delay: 5
		Interval: 5
		RequiresCondition: c4
	AmbientSound@c4seal:
		SoundFiles: sealc4tick1.aud, sealc4tick2.aud
		Delay: 9
		Interval: 9
		RequiresCondition: c4seal
	ExternalCondition@SealC4Progress:
		Condition: c4seal-preparing
	GrantChargingCondition@SealC4Progress:
		MaxCharge: 100 # 4x the reload delay of PrepareC4Seal which grants a condition for 25 ticks, so after 4 prepares the C4 will be planted
		Condition: c4seal-prepared
		ShowSelectionBarWhenEmpty: false
		ShowSelectionBarWhenFull: false
		ChargingColor: ff0000
		RequiresCondition: c4seal-preparing
	Targetable@SealC4Progress: # Only required for yaml warning, as c4seal-prepared isn't used for anything (GrantChargingCondition just used for the progress bar)
		TargetTypes: SealC4Prepared
		RequiresCondition: c4seal-prepared

^TNTPlantable:
	DelayedWeaponAttachable@TNT:
		Type: tnt
		Condition: tnt
		ProgressBarColor: FF000080
		AttachLimit: 10
	Targetable@TNTPlantable:
		TargetTypes: TNTPlantable
	Targetable@TNTAttached:
		TargetTypes: TNTAttached
		RequiresCondition: tnt
	WithDecoration@tnt:
		Image: tnt
		Sequence: tnt
		Position: CenterCenter
		RequiresCondition: tnt
		RequiresSelection: False
		ValidRelationships: Ally, Neutral, Enemy
	AmbientSound@tnt:
		SoundFiles: icraloop.aud
		Delay: 6
		Interval: 6
		RequiresCondition: tnt
	WithTextDecoration@2tnt:
		Text: 2
		RequiresCondition: tnt == 2
		Position: CenterCenter
	WithTextDecoration@3tnt:
		Text: 3
		RequiresCondition: tnt == 3
		Position: CenterCenter
	WithTextDecoration@4tnt:
		Text: 4
		RequiresCondition: tnt == 4
		Position: CenterCenter
	WithTextDecoration@5tnt:
		Text: 5
		RequiresCondition: tnt == 5
		Position: CenterCenter
	WithTextDecoration@6tnt:
		Text: 6+
		RequiresCondition: tnt >= 6
		Position: CenterCenter

^MindControllable:
	MindControllable@MINDCONTROL:
		ControlType: MindControl
		ControlledCondition: mindcontrolled
	Targetable@MINDCONTROL:
		TargetTypes: MindControllable
		RequiresCondition: !mindcontrolled
	MindControllableProgressBar@MINDCONTROL:
		ControlTypes: MindControl
		Color: ff00ff
		RequiresCondition: !mindcontrolled
	WithIdleOverlay@MINDCONTROL:
		Sequence: mind-overlay
		Palette: scrin
		RequiresCondition: mindcontrolled
		Offset: 0,0,300
		IsDecoration: True
	RejectsOrders@MindControlled:
		Reject: Unload
		RequiresCondition: mindcontrolled

^Hackable:
	MindControllable@HACKABLE:
		ControlType: Hack
		ControlledCondition: hacked
		RevokingCondition: restoring
	Targetable@HACKABLE:
		TargetTypes: Hackable
		RequiresCondition: !hacked
	MindControllableProgressBar@HACKABLE:
		ControlTypes: Hack
		Color: 1ce312
		RequiresCondition: !hacked
	WithDecoration@HACKED:
		Image: hacked
		Sequence: hacked
		RequiresCondition: hacked && !restoring
		Position: Center
		Palette: effect
		ValidRelationships: Ally, Neutral, Enemy
	WithDecoration@RESTORING:
		Image: hacked
		Sequence: restoring
		RequiresCondition: restoring
		Position: Center
		Palette: effect
		ValidRelationships: Ally, Neutral, Enemy
	PowerMultiplier@HACKED:
		RequiresCondition: hacked
		Modifier: 0

^HackableOverloadable:
	Inherits: ^Hackable
	WithDecoration@HACKED:
		Sequence: hacked-overloading
	WithDecoration@OVERLOADING:
		Image: hacked
		Sequence: overloading
		RequiresCondition: overloading
		Position: Center
		Palette: effect
		ValidRelationships: Ally, Neutral, Enemy
	ChangesHealth@HACKED:
		Step: -1500
		StartIfBelow: 101
		Delay: 20
		RequiresCondition: (hacked && !restoring) || overloading
	ExternalCondition@OVERLOADING:
		Condition: overloading
	PowerMultiplier@OVERLOADING:
		Modifier: 0
		RequiresCondition: overloading
	WithPalettedOverlay@OVERLOADING:
		RequiresCondition: overloading
		Palette: overload

^TechLockable:
	InfiltrateForTimedCondition@TechLock:
		Condition: tech-locked
		Duration: 750
		Types: TechLockInfiltrate
		InfiltrationNotification: TechnologyLocked
		InfiltratedNotification: OurTechnologyLocked
		ShowSelectionBar: true
		PlayerExperience: 15
	WithDecoration@TechLock:
		Image: hacked
		Sequence: techlocked
		RequiresCondition: tech-locked && !hacked
		Position: Center
		Palette: effect
		ValidRelationships: Ally, Neutral, Enemy
	Targetable@TechLock:
		RequiresCondition: !being-warped
		TargetTypes: TechLockInfiltrate

^GeneticallyMutatable:
	ExternalCondition@GMUT:
		Condition: geneticmutation
	SpawnActorOnDeath@GMUT:
		Actor: brut.mutating
		OwnerType: Killer
		DeathType: MutatedDeath
		SpawnAfterDefeat: false
		SkipMakeAnimations: false
		RequiresCondition: geneticmutation
	Targetable@GMUT:
		TargetTypes: GeneticallyMutatable
		RequiresCondition: geneticmutation

^MiniDroneAttachable:
	Targetable@MINIDRONE:
		TargetTypes: MiniDroneAttachable
		RequiresCondition: !mdrn-attached
	ChangesHealth@MINIDRONE:
		Step: 1000
		Delay: 25
		StartIfBelow: 100
		DamageCooldown: 100
		RequiresCondition: mdrn-attached
	AttachableTo@MINIDRONE:
		Type: MiniDrone
		Limit: 1
		LimitCondition: mdrn-attached

^ObserverAttachable:
	GrantTimedCondition@ObserverIcon:
		Condition: observer-icon-hidden
		Duration: 750
		RequiresCondition: observer-attached
	WithDecoration@ObserverIconWatched:
		Image: watched
		Sequence: idle
		Palette: scrineffect
		Position: Top
		ValidRelationships: Ally
		Margin: 0, 8
		RequiresCondition: observer-attached && !observer-icon-hidden
	WithDecoration@ObserverIconWatcher:
		Image: watched
		Sequence: idle
		Palette: scrineffect
		Position: Top
		ValidRelationships: Enemy, Neutral
		Margin: 0, 8
		RequiresCondition: observer-attached

^WatcherParasiteAttachable:
	Inherits@ObserverAttachable: ^ObserverAttachable
	Targetable@WatcherParasite:
		TargetTypes: WatcherParasiteAttachable
		RequiresCondition: !observer-attached
	AttachableTo@WatcherParasite:
		Type: WatcherParasite
		Limit: 1
		AttachedCondition: observer-attached

^ShadowBeaconAttachable:
	Inherits@ObserverAttachable: ^ObserverAttachable
	Targetable@ShadowBeacon:
		TargetTypes: ShadowBeaconAttachable
		RequiresCondition: !observer-attached
	AttachableTo@ShadowBeacon:
		Type: ShadowBeacon
		Limit: 1
		AttachedCondition: observer-attached

^TargetPaintable:
	ExternalCondition@PaintedTarget:
		Condition: painted-target
	DamageMultiplier@PaintedTarget:
		Modifier: 110
		RequiresCondition: painted-target
	WithDecoration@PaintedTarget:
		Image: targetpainter
		Sequence: idle
		Palette: effect
		Position: Top
		ValidRelationships: Ally, Enemy, Neutral
		Margin: 0, 8
		RequiresCondition: painted-target
	WithFlashEffect@PaintedTarget:
		Color: ff0000
		Interval: 25
		RequiresCondition: painted-target

^HoverTrail:
	LeavesTrails:
		Image: twake
		Palette: effect
		TerrainTypes: Water
		TrailWhileStationary: true
		StationaryInterval: 18
		MovingInterval: 6

^AutoTargetGround:
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry, Vehicle, Water, Underwater, Defense
		InvalidTargets: NoAutoTarget, WaterStructure, AntiAirDefense
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything
		ValidTargets: Structure, AntiAirDefense, Mine
		InvalidTargets: NoAutoTarget

^AutoTargetGroundAssaultMove:
	Inherits: ^AutoTargetGround
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything || assault-move
	AttackMove:
		AssaultMoveCondition: assault-move

^AutoTargetGroundAssaultMovePrioritizeVehicles:
	Inherits: ^AutoTargetGroundAssaultMove
	AutoTargetPriority@DEFAULTVEH:
		ValidTargets: Vehicle, Ship
		InvalidTargets: NoAutoTarget
		Priority: 10

^AutoTargetGroundAssaultMovePrioritizeInfantry:
	Inherits: ^AutoTargetGroundAssaultMove
	AutoTargetPriority@DEFAULTINF:
		ValidTargets: Infantry
		InvalidTargets: NoAutoTarget
		Priority: 10

^AutoTargetNavalAssaultMove:
	Inherits: ^AutoTargetGroundAssaultMove
	AutoTargetPriority@DEFAULT:
		InvalidTargets: NoAutoTarget

^AutoTargetAir:
	AutoTarget:
	AutoTargetPriority@DEFAULT:
		ValidTargets: Air, AirSmall
		InvalidTargets: NoAutoTarget

^AutoTargetAirICBM:
	Inherits: ^AutoTargetAir
	AutoTargetPriority@DEFAULT:
		ValidTargets: Air, AirSmall, ICBM

^AutoTargetAll:
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry, Vehicle, Water, Underwater, Air, AirSmall, Defense
		InvalidTargets: NoAutoTarget, WaterStructure, AntiAirDefense
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything
		ValidTargets: Structure, AntiAirDefense, Mine
		InvalidTargets: NoAutoTarget

^AutoTargetAllAssaultMove:
	Inherits: ^AutoTargetAll
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything || assault-move
	AttackMove:
		AssaultMoveCondition: assault-move

^AutoTargetAllAssaultMovePrioritizeGround:
	Inherits: ^AutoTargetAllAssaultMove
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry, Vehicle, Water, Underwater, Defense
	AutoTargetPriority@DeprioritizedAir:
		ValidTargets: Air, AirSmall
		InvalidTargets: NoAutoTarget
		Priority: 0

^AutoTargetAllAssaultMovePrioritizeAir:
	Inherits: ^AutoTargetAllAssaultMove
	AutoTargetPriority@DEFAULTAA:
		ValidTargets: Air, AirSmall
		InvalidTargets: NoAutoTarget
		Priority: 10

^AutoTargetAllNavalAssaultMove:
	Inherits: ^AutoTargetAllAssaultMove
	AutoTargetPriority@DEFAULT:
		InvalidTargets: NoAutoTarget

^GlobalBounty:
	GrantConditionOnPrerequisite@GLOBALBOUNTY:
		Condition: global-bounty
		Prerequisites: global-bounty
	GivesBountyCA:
		RequiresCondition: global-bounty
		Percentage: 15

^EconomyPolicyDiscount:
	ProductionCostMultiplier@EconomyPolicy1:
		Multiplier: 90
		Prerequisites: economy.policy, influence.level1, !influence.level2
	ProductionCostMultiplier@EconomyPolicy2:
		Multiplier: 80
		Prerequisites: economy.policy, influence.level2, !influence.level3
	ProductionCostMultiplier@EconomyPolicy3:
		Multiplier: 70
		Prerequisites: economy.policy, influence.level3

^EconomyPolicyTimeReduction:
	ProductionTimeMultiplier@EconomyPolicy1:
		Multiplier: 90
		Prerequisites: economy.policy, influence.level1, !influence.level2
	ProductionTimeMultiplier@EconomyPolicy2:
		Multiplier: 80
		Prerequisites: economy.policy, influence.level2, !influence.level3
	ProductionTimeMultiplier@EconomyPolicy3:
		Multiplier: 70
		Prerequisites: economy.policy, influence.level3

^DefensePolicyDamageReduction:
	GrantConditionOnPrerequisite@DefensePolicy1:
		Condition: defense-policy1
		Prerequisites: defense.policy, influence.level1, !influence.level2
	GrantConditionOnPrerequisite@DefensePolicy2:
		Condition: defense-policy2
		Prerequisites: defense.policy, influence.level2, !influence.level3
	GrantConditionOnPrerequisite@DefensePolicy3:
		Condition: defense-policy3
		Prerequisites: defense.policy, influence.level3
	DamageMultiplier@DefensePolicy1:
		Modifier: 91
		RequiresCondition: defense-policy1
	DamageMultiplier@DefensePolicy2:
		Modifier: 83
		RequiresCondition: defense-policy2
	DamageMultiplier@DefensePolicy3:
		Modifier: 77
		RequiresCondition: defense-policy3
	ChangesHealth@DefensePolicy2:
		Step: 200
		StartIfBelow: 100
		Delay: 24
		DamageCooldown: 0
		RequiresCondition: defense-policy2 && being-repaired

^DefensePolicyDamageBonus:
	GrantConditionOnPrerequisite@DefensePolicy3:
		Condition: defense-policy3
		Prerequisites: defense.policy, influence.level3
	FirepowerMultiplier@DefensePolicy3:
		Modifier: 115
		RequiresCondition: defense-policy3

^DevelopmentPolicyVeterancyBonus:
	GrantConditionOnPrerequisite@DevelopmentPolicy1:
		Condition: development-policy1
		Prerequisites: development.policy, influence.level1, !influence.level2
	GrantConditionOnPrerequisite@DevelopmentPolicy2:
		Condition: development-policy2
		Prerequisites: development.policy, influence.level2, !influence.level3
	GrantConditionOnPrerequisite@DevelopmentPolicy3:
		Condition: development-policy3
		Prerequisites: development.policy, influence.level3
	GainsExperienceMultiplier@DevelopmentPolicy1:
		Modifier: 120
		RequiresCondition: development-policy1
	GainsExperienceMultiplier@DevelopmentPolicy2:
		Modifier: 140
		RequiresCondition: development-policy2
	GainsExperienceMultiplier@DevelopmentPolicy3:
		Modifier: 160
		RequiresCondition: development-policy3

^DevelopmentPolicyDiscount:
	ProductionCostMultiplier@DevelopmentPolicy2:
		Multiplier: 90
		Prerequisites: development.policy, influence.level2

^SovietT4Boost:
	ProductionTimeMultiplier@IndustrialPlantBoost:
		Multiplier: 95
		Prerequisites: indp, !indplowpower
	ProductionCostMultiplier@IndustrialPlantBoost:
		Multiplier: 90
		Prerequisites: indp, !indplowpower
	ProductionTimeMultiplier@MunitionsPlantBoost:
		Multiplier: 85
		Prerequisites: munp, !munplowpower
	GrantConditionOnPrerequisite@MunitionsPlantBoost:
		Condition: munp-boost
		Prerequisites: munp, !munplowpower
	ReloadDelayMultiplier@MunitionsPlantBoost:
		RequiresCondition: munp-boost
		Modifier: 85

^OilRefCostReduction:
	ProductionCostMultiplier@OilRef:
		Multiplier: 90
		Prerequisites: oilr

^TDPalette:
	RenderSprites:
		PlayerPalette: playertd

^Vehicle-NOUPG:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^IronCurtainable
	Inherits@3: ^SpriteActor
	Inherits@4: ^Cloakable
	Inherits@5: ^EmpDisable
	Inherits@9: ^Warpable
	Inherits@10: ^Frenzy
	Inherits@11: ^NaniteRepair
	Inherits@12: ^Veilable
	Inherits@13: ^BotGroundProductionBonus
	Inherits@14: ^Chillable
	Inherits@15: ^Slowable
	Inherits@16: ^AffectedByDriverKill
	Inherits@17: ^Concussion
	Inherits@18: ^Suppressable
	Inherits@19: ^CrateCloak
	Inherits@20: ^WeaponJammable
	Inherits@21: ^AuraRepairable
	Inherits@C4Plantable: ^C4Plantable
	Inherits@TNTPlantable: ^TNTPlantable
	Inherits@bounty: ^GlobalBounty
	Inherits@SovietT4Boost: ^SovietT4Boost
	Inherits@oilcost: ^OilRefCostReduction
	Inherits@selection: ^SelectableCombatUnit
	Inherits@chrono: ^Chronoshiftable
	Inherits@mind: ^MindControllable
	Inherits@handicaps: ^PlayerHandicaps
	Inherits@irrad: ^Irradiatable
	Inherits@ionsurge: ^IonSurgable
	Inherits@atomize: ^Atomizable
	Inherits@minidrone: ^MiniDroneAttachable
	Inherits@WatcherParasite: ^WatcherParasiteAttachable
	Inherits@ShadowBeacon: ^ShadowBeaconAttachable
	Inherits@TargetPaintable: ^TargetPaintable
	Inherits@lchrheal: ^LeecherHealable
	Inherits@Blindable: ^Blindable
	Inherits@KillZoneTargetable: ^KillZoneTargetable
	Inherits@Anathema: ^Anathema
	Inherits@DevelopmentPolicyVeterancyBonus: ^DevelopmentPolicyVeterancyBonus
	Huntable:
	EdibleByLeap:
	OwnerLostAction:
		Action: Kill
	UpdatesPlayerStatistics:
	Mobile:
		PauseOnCondition: being-captured || empdisable || being-warped || driver-dead || notmobile
		Locomotor: wheeled
		TurnSpeed: 20
	Selectable:
		Bounds: 1024, 1024
	Targetable:
		TargetTypes: Ground, Vehicle
		RequiresCondition: !parachute && !being-warped
	Targetable@C4Plantable:
		RequiresCondition: !parachute && !being-warped
	Targetable@TNTPlantable:
		RequiresCondition: !parachute && !being-warped
	Targetable@REPAIR:
		RequiresCondition: !parachute && !being-warped && damaged && !repair-cooldown
		TargetTypes: Repair
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	Repairable:
		RepairActors: fix, rep, srep
	Passenger:
		CargoType: Vehicle
	AttackMove:
	HiddenUnderFog:
	ActorLostNotification:
		TextNotification: Unit lost.
	WithDamageOverlay:
	Guard:
	Guardable:
	Tooltip:
		GenericName: Vehicle
	CaptureManager:
		BeingCapturedCondition: being-captured
	Capturable:
		Types: vehicle
		CancelActivity: True
		RequiresCondition: !being-warped
	CaptureNotification:
		Notification: EnemyUnitStolen
		TextNotification: Enemy unit stolen.
		LoseNotification: UnitStolen
		LoseTextNotification: Unit stolen.
	MustBeDestroyed:
	Voiced:
		VoiceSet: VehicleVoice
	Parachutable:
		FallRate: 26
		KilledOnImpassableTerrain: true
		ParachutingCondition: parachute
	FireWarheadsOnDeath:
		Weapon: UnitExplodeSmall
		EmptyWeapon: UnitExplodeSmall
		RequiresCondition: !being-warped
	WithFacingSpriteBody:
	WithParachute:
		ShadowImage: parach-largeshadow
		ShadowSequence: idle
		Image: parachl
		Sequence: idle
		OpeningSequence: open
		Offset: 0,0,327
		RequiresCondition: parachute
	HitShape:
	MapEditorData:
		Categories: Vehicle
	GrantConditionOnPrerequisite@ROF:
		Condition: revealonfire
		Prerequisites: global.revealonfire
	RevealOnFire:
		RevealGeneratedShroud: True
		RequiresCondition: revealonfire
	RevealOnDeath:
		Duration: 100
		Radius: 2c512
	Carryable:
		CarriedCondition: passenger
		LockedCondition: notmobile
		LocalOffset: 0,0,200
	GrantCondition@CarriedImmobile:
		Condition: notmobile
		RequiresCondition: passenger
	CancelActivityOnPickup:
	Crushable:
		CrushClasses: tank
		WarnProbability: 0
		RequiresCondition: !invulnerability && !being-warped
	DamagedByTintedCells@RADSTRONG:
		Damage: 100
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.strong
	DamagedByTintedCells@RADMED:
		Damage: 40
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.medium
	DetectCloaked@UNDERBRIDGE:
		DetectionTypes: Underbridge
		Range: 5c0
	GpsRadarDot:
		Sequence: Vehicle
	TeleportNetworkTransportable:
	Targetable@MINDCONTROL:
		RequiresCondition: !mindcontrolled && !invulnerability && !driver-dead
	GrantConditionOnHealingReceived@REPAIRCOOLDOWN:
		Condition: repair-cooldown
		RequiredHealing: 85000
		StackDuration: 1000
		MinimumHealing: 2500
		DamageTypes: DirectRepair
		ShowSelectionBar: true
	ExternalCondition@UNITSELL:
		Condition: unit-sellable
	Sellable:
		RequiresCondition: unit-sellable && !c4 && !being-captured && !being-warped
		SellSounds: cashturntd.aud
		Notification: UnitSold
		TextNotification: Unit sold.
		Cursor: sell2

^Vehicle:
	Inherits@1: ^Vehicle-NOUPG
	Inherits@GDIUPG: ^GDIUpgrade
	Inherits@GDIBINO: ^BinoUpgrade

^VehicleTD-NOUPG:
	Inherits@1: ^Vehicle-NOUPG
	Inherits@TDPAL: ^TDPalette
	WithDamageOverlay:
		Image: smoke_mtd
	Cloak@CRATE-CLOAK:
		Palette: cloak

^VehicleTD:
	Inherits@1: ^VehicleTD-NOUPG
	Inherits@GDIUPG: ^GDIUpgrade
	Inherits@GDIBINO: ^BinoUpgrade

^Tank:
	Inherits: ^Vehicle
	Mobile:
		Locomotor: tracked

^TankTD:
	Inherits: ^VehicleTD
	Mobile:
		Locomotor: tracked

^SlowedByCrushing:
	ExternalCondition@CRUSHATTEMPTSLOW:
		Condition: crush-attempt-slow
	SpeedMultiplier@CRUSHATTEMPTSLOW:
		RequiresCondition: crush-attempt-slow && !crush-slow
		Modifier: 55
	ExternalCondition@CRUSHSLOW:
		Condition: crush-slow
	SpeedMultiplier@CRUSHSLOW:
		RequiresCondition: crush-slow
		Modifier: 35

^SlightlySlowedByCrushing:
	Inherits@CRUSHSLOW: ^SlowedByCrushing
	SpeedMultiplier@CRUSHATTEMPTSLOW:
		Modifier: 70
	SpeedMultiplier@CRUSHSLOW:
		Modifier: 50

^Infantry:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^GainsExperience
	Inherits@3: ^AuraHealable
	Inherits@4: ^SpriteActor
	Inherits@5: ^HazmatUpgrade
	Inherits@7: ^Warpable
	Inherits@9: ^IronCurtainable-Kills
	Inherits@10: ^FrenzyInfantry
	Inherits@11: ^VeilableInfantry
	Inherits@12: ^BotGroundProductionBonus
	Inherits@13: ^Chillable
	Inherits@14: ^Concussion
	Inherits@15: ^Suppressable
	Inherits@16: ^CrateCloak
	Inherits@17: ^Slowable
	Inherits@CLOAK: ^Cloakable
	Inherits@TNTPlantable: ^TNTPlantable
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableCombatUnit
	Inherits@mind: ^MindControllable
	Inherits@GMUT: ^GeneticallyMutatable
	Inherits@handicaps: ^PlayerHandicaps
	Inherits@BINOS: ^BinoUpgradeInfantry
	Inherits@oilcost: ^OilRefCostReduction
	Inherits@ionsurge: ^IonSurgableInfantry
	Inherits@lchrheal: ^LeecherHealable
	Inherits@atomize: ^AtomizableInfantry
	Inherits@Blindable: ^Blindable
	Inherits@KillZoneTargetable: ^KillZoneTargetable
	Inherits@ReaperSnareable: ^ReaperSnareable
	Inherits@IdolOfKane: ^IdolOfKane
	Inherits@DevelopmentPolicyVeterancyBonus: ^DevelopmentPolicyVeterancyBonus
	RenderSprites:
		PlayerPalette: playertd
	Huntable:
	OwnerLostAction:
		Action: Kill
		DeathTypes: DefaultDeath
	Health:
		HP: 2500
	Armor:
		Type: None
	RevealsShroud:
		Range: 4c0
	Mobile:
		Speed: 46
		Locomotor: foot
		PauseOnCondition: being-warped || reapersnare
	GrantConditionOnTerrain@ONTIB:
		Condition: on-tib
		TerrainTypes: Tiberium, BlueTiberium, BlackTiberium
	GrantStackingCondition@TIBEXPOSED:
		Condition: tibexposed
		RequiresCondition: on-tib
		DelayPerInstance: 250
		RevokeDelay: 200
	DamagedByTerrain@TIBDAMAGE:
		Terrain: Tiberium, BlueTiberium, BlackTiberium
		Damage: 125
		DamageInterval: 16
		DamageTypes: ToxinDeath
		RequiresCondition: tibexposed && !(hazmatsuits || hazmatsuits-soviet || being-warped || parachute)
	GrantConditionOnPrerequisite@BIO:
		Condition: hazmatsuits
		Prerequisites: bio
	SpawnActorOnDeath:
		Probability: 35
		Actor: vice
		OwnerType: InternalName
		InternalOwner: Creeps
		DeathType: ToxinDeath
		RequiresLobbyCreeps: false
	Selectable:
		Bounds: 768, 768, 0, -256
		DecorationBounds: 512, 725, 0, -256
	Targetable:
		RequiresCondition: !parachute && !being-warped
		TargetTypes: Ground, Infantry
	Targetable@SpyDisguise:
		TargetTypes: SpyDisguise
	Targetable@TNTPlantable:
		RequiresCondition: !parachute && !being-warped
	Targetable@HEAL:
		RequiresCondition: !parachute && !being-warped && damaged
		TargetTypes: Heal
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	QuantizeFacingsFromSequence:
		Sequence: stand
	WithInfantryBody:
		RequiresCondition: !parachute && !being-warped && !reapersnare
	WithInfantryBody@Parachute:
		StandSequences: parachute
		RequiresCondition: parachute || reapersnare
	WithInfantryBody@Warped:
		StandSequences: stand
		RequiresCondition: being-warped
	WithDeathAnimation:
		DeathSequencePalette: playertd
		DeathTypes:
			DefaultDeath: 1
			BulletDeath: 2
			SmallExplosionDeath: 3
			ExplosionDeath: 4
			FireDeath: 5
			ElectricityDeath: 6
			PoisonDeath: 7
			ChronoDeath: 8
			ToxinDeath: 9
			RadiationDeath: 10
			FrozenDeath: 11
			AtomizedDeath: 12
		CrushedSequence: die-crushed
		CrushedPaletteIsPlayerPalette: true
		CrushedSequencePalette: player
	AttackMove:
	Passenger:
		CargoType: Infantry
		CargoCondition: passenger
	UnusedCondition@Passenger:
		Condition: passenger
	MassEntersCargo:
	PassengerBlocked:
	HiddenUnderFog:
	ActorLostNotification:
		TextNotification: Unit lost.
	Crushable:
		WarnProbability: 100
		CrushSound: squishy2.aud
		RequiresCondition: !invulnerability && !being-warped
	Guard:
	Guardable:
	Tooltip:
		GenericName: Soldier
	DeathSounds@NORMAL:
		Voice: Die
		DeathTypes: DefaultDeath, BulletDeath, SmallExplosionDeath, ExplosionDeath, FrozenDeath
	DeathSounds@BURNED:
		Voice: Burned
		DeathTypes: FireDeath
	DeathSounds@ZAPPED:
		Voice: Zapped
		DeathTypes: ElectricityDeath, ChronoDeath, AtomizedDeath
	DeathSounds@POISONED:
		Voice: Poisoned
		DeathTypes: PoisonDeath, ToxinDeath, RadiationDeath, MutatedDeath
	Parachutable:
		FallRate: 26
		KilledOnImpassableTerrain: true
		GroundCorpseSequence: corpse
		GroundImpactSound: squishy2.aud
		WaterImpactSound: splash9.aud
		WaterCorpseSequence: small_splash
		ParachutingCondition: parachute
	Voiced:
		VoiceSet: GenericVoice
	WithParachute:
		ShadowImage: parach-shadow
		ShadowSequence: idle
		Image: parach
		Sequence: idle
		OpeningSequence: open
		Offset: 0,0,427
		RequiresCondition: parachute
	HitShape:
		Type: Circle
			Radius: 128
	MapEditorData:
		Categories: Infantry
	EdibleByLeap:
	GrantConditionOnPrerequisite@ROF:
		Condition: revealonfire
		Prerequisites: global.revealonfire
	RevealOnFire:
		RevealGeneratedShroud: True
		RequiresCondition: revealonfire
	RevealOnDeath:
		Duration: 100
	DetectCloaked:
		DetectionTypes: Cloak
		Range: 1c512
	DamagedByTintedCells@RADSTRONG:
		Damage: 750
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.strong
		RequiresCondition: !(hazmatsuits || being-warped || parachute || hazmatsuits-soviet)
	DamagedByTintedCells@RADSTRONGHAZMAT:
		Damage: 375
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.strong
		RequiresCondition: hazmatsuits && !(being-warped || parachute || hazmatsuits-soviet)
	DamagedByTintedCells@RADSTRONGHAZMATSOVIET:
		Damage: 25
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.strong
		RequiresCondition: hazmatsuits-soviet && !(being-warped || parachute || hazmatsuits)
	DamagedByTintedCells@RADMED:
		Damage: 300
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.medium
		RequiresCondition: !(hazmatsuits || being-warped || parachute || hazmatsuits-soviet)
	DamagedByTintedCells@RADMEDHAZMAT:
		Damage: 150
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.medium
		RequiresCondition: hazmatsuits && !(being-warped || parachute || hazmatsuits-soviet)
	DamagedByTintedCells@RADWEAK:
		Damage: 100
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.weak
		RequiresCondition: !(hazmatsuits || being-warped || parachute || hazmatsuits-soviet)
	DamagedByTintedCells@RADWEAKHAZMAT:
		Damage: 50
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.weak
		RequiresCondition: hazmatsuits && !(being-warped || parachute || hazmatsuits-soviet)
	DamagedByTintedCells@FROZEN:
		Damage: 50
		DamageInterval: 16
		DamageTypes: FrozenDeath
		LayerName: cryoresidue
		RequiresCondition: !(being-warped || parachute)
	GpsRadarDot:
		Sequence: Infantry
	GrantExternalConditionToCrusher@CRUSHATTEMPTSLOW:
		WarnCrushCondition: crush-attempt-slow
		WarnCrushDuration: 50
		OnCrushCondition: crush-attempt-slow
		OnCrushDuration: 100
	GrantExternalConditionToCrusher@CRUSHSLOW:
		OnCrushCondition: crush-slow
		OnCrushDuration: 50
	TeleportNetworkTransportable:
	DamageMultiplier@MINDCONTROL:
		Modifier: 70
		RequiresCondition: mindcontrolled
	WithIdleOverlay@LCHRHEAL:
		Sequence: infantry
		Offset: 0,0,300
	Cloak@TIBSTEALTH:
		CloakSound: cloak5sm.aud
		UncloakSound: appear1sm.aud
	Cloak@SGENCLOAK:
		CloakSound: cloak5sm.aud
		UncloakSound: appear1sm.aud

^Soldier:
	Inherits: ^Infantry
	Inherits@DRIVER: ^CanCaptureDriverlessVehicles
	Inherits@ZoneDefenderShieldable: ^ZoneDefenderShieldable
	UpdatesPlayerStatistics:
	MustBeDestroyed:
	RevealsShroud:
		Range: 5c0
	TakeCover:
		SpeedModifier: 60
		Duration: 75
		DamageModifiers:
			Prone50Percent: 50
		DamageTriggers: TriggerProne
		PauseOnCondition: being-warped
		RequiresCondition: !reapersnare
	WithInfantryBody:
		IdleSequences: idle1,idle2
		StandSequences: stand,stand2
	DamageTypeDamageMultiplier@FLAKARMOR:
		DamageTypes: FlakVestMitigated
		Modifier: 60
		RequiresCondition: flakarmor-upgrade
	DamageTypeDamageMultiplier@FLAKARMORMINOR:
		DamageTypes: FlakVestMitigatedMinor
		Modifier: 80
		RequiresCondition: flakarmor-upgrade
	GrantConditionOnPrerequisite@FLAKARMOR:
		Condition: flakarmor-upgrade
		Prerequisites: flakarmor.upgrade
	GrantTimedConditionOnCrushWarning@QUICKDODGE:
		Condition: quickdodge
		Duration: 20
	SpeedMultiplier@QUICKDODGE:
		RequiresCondition: quickdodge
		Modifier: 200
	DamageMultiplier@QUICKDODGE:
		RequiresCondition: quickdodge
		Modifier: 50

^Cyborg:
	Inherits: ^Soldier
	Inherits@CYBORGUPG: ^NodCyborgUpgrade
	Inherits@SLOWABLE: ^Slowable
	Targetable@Cyborg:
		TargetTypes: Cyborg
		RequiresCondition: !parachute && !being-warped
	ExternalCondition@ONTIB:
		Condition: on-tib
	ExternalCondition@ONBLUETIB:
		Condition: on-bluetib
	ChangesHealth@THEAL:
		Step: 0
		PercentageStep: 1
		Delay: 15
		StartIfBelow: 100
		DamageCooldown: 150
		RequiresCondition: on-tib || on-bluetib
		DamageTypes: ToxinDeath
	WithDecoration@REDCROSS:
		RequiresCondition: on-tib || on-bluetib || (hospitalheal && damaged)
	WithDecoration@RANK-1:
		BlinkPatterns:
			on-tib || (damaged && hospitalheal): On, Off
	WithDecoration@RANK-2:
		BlinkPatterns:
			on-tib || (damaged && hospitalheal): On, Off
	WithDecoration@RANK-ELITE:
		BlinkPatterns:
			on-tib || (damaged && hospitalheal): On, Off
	-GrantConditionOnPrerequisite@BIO:
	-GrantConditionOnPrerequisite@HAZMAT:
	-GrantConditionOnPrerequisite@HAZMATSOVIET:
	-GrantConditionOnPrerequisite@HAZMATZOCOM:
	-GrantCondition@HAZMAT:
	-GrantCondition@HAZMATSOVIET:
	-WithDecoration@HAZMAT:
	-WithDecoration@HAZMATSOVIET:
	-GrantConditionOnTerrain@HAZMATSOVIET:
	-SpeedMultiplier@HAZMATSOVIET:
	-Targetable@HAZMAT:
	-DamagedByTerrain@TIBDAMAGE:
	-GrantStackingCondition@TIBEXPOSED:
	DamagedByTintedCells@RADSTRONG:
		Damage: 375
		RequiresCondition: !being-warped
	-DamagedByTintedCells@RADSTRONGHAZMAT:
	-DamagedByTintedCells@RADSTRONGHAZMATSOVIET:
	-DamagedByTintedCells@RADMED:
	-DamagedByTintedCells@RADMEDHAZMAT:
	-DamagedByTintedCells@RADWEAK:
	-DamagedByTintedCells@RADWEAKHAZMAT:
	-DamageTypeDamageMultiplier@FLAKARMOR:
	-DamageTypeDamageMultiplier@FLAKARMORMINOR:
	-GrantConditionOnPrerequisite@FLAKARMOR:
	WithDecoration@BINO:
		-BlinkPatterns:

^CivInfantry:
	Inherits: ^Infantry
	-RenderSprites:
	RenderSprites:
		Palette: temptd
	WithDeathAnimation:
		DeathPaletteIsPlayerPalette: false
		DeathSequencePalette: temptd
	Selectable:
		Class: CivInfantry
	Valued:
		Cost: 10
	Tooltip:
		Name: Civilian
		ShowOwnerRow: false
	RevealsShroud:
		Range: 3c0
	Passenger:
		CustomPipType: gray
	Voiced:
		VoiceSet: CivilianMaleVoice
	Wanders:
		MinMoveDelay: 150
		MaxMoveDelay: 750
		AvoidTerrainTypes: Tiberium, BlueTiberium, BlackTiberium
	MapEditorData:
		Categories: Civilian infantry

^ArmedCivilian:
	Inherits@civInfantry: ^CivInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Tooltip:
		RequiresCondition: !owned-by-combatant
	Tooltip@COMBATANT:
		Name: Technician
		RequiresCondition: owned-by-combatant
	GrantConditionOnCombatantOwner@COMBATANT:
		Condition: owned-by-combatant
	Convertible:
		SpawnActors: N1C
	GrantConditionOnFaction@RA:
		Factions: allies, england, france, germany, usa, soviet, russia, ukraine, iraq, yuri
		Condition: ra-faction
	GrantConditionOnFaction@TD:
		Factions: gdi, zocom, eagle, talon, arc, nod, blackh, legion, marked, shadow
		Condition: td-faction
	Armament@RA:
		Weapon: Pistol
		RequiresCondition: ra-faction
	Armament@TD:
		Weapon: PistolTD
		RequiresCondition: td-faction
	AttackFrontal:
		FacingTolerance: 0
	WithInfantryBody:
		DefaultAttackSequence: shoot

^Ship:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^GainsExperience
	Inherits@3: ^IronCurtainable
	Inherits@4: ^SpriteActor
	Inherits@5: ^EmpDisable
	Inherits@6: ^Cloakable
	Inherits@7: ^GDIUpgrade
	Inherits@8: ^BinoUpgrade
	Inherits@10: ^Warpable
	Inherits@11: ^Frenzy
	Inherits@12: ^NaniteRepair
	Inherits@13: ^Veilable
	Inherits@14: ^Chillable
	Inherits@15: ^BotGroundProductionBonus
	Inherits@16: ^Concussion
	Inherits@17: ^Suppressable
	Inherits@18: ^CrateCloak
	Inherits@19: ^WeaponJammable
	Inherits@20: ^AuraRepairable
	Inherits@slowable: ^Slowable
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableCombatUnit
	Inherits@SovietT4Boost: ^SovietT4Boost
	Inherits@oilcost: ^OilRefCostReduction
	Inherits@chrono: ^Chronoshiftable
	Inherits@mind: ^MindControllable
	Inherits@handicaps: ^PlayerHandicaps
	Inherits@atomize: ^Atomizable
	Inherits@C4Plantable: ^C4Plantable
	Inherits@TNTPlantable: ^TNTPlantable
	Inherits@minidrone: ^MiniDroneAttachable
	Inherits@WatcherParasite: ^WatcherParasiteAttachable
	Inherits@TargetPaintable: ^TargetPaintable
	Inherits@Blindable: ^Blindable
	Inherits@KillZoneTargetable: ^KillZoneTargetable
	Inherits@Anathema: ^Anathema
	Inherits@DevelopmentPolicyVeterancyBonus: ^DevelopmentPolicyVeterancyBonus
	Huntable:
	RenderSprites:
		PlayerPalette: playernavy
	OwnerLostAction:
		Action: Kill
	UpdatesPlayerStatistics:
	Mobile:
		Locomotor: naval
		PauseOnCondition: empdisable || being-warped
	Selectable:
		Bounds: 1024, 1024
	Targetable:
		TargetTypes: Ground, Water, Ship
		RequiresCondition: !being-warped
	Targetable@C4Plantable:
		RequiresCondition: !being-warped
	Targetable@TNTPlantable:
		RequiresCondition: !being-warped
	Targetable@REPAIR:
		RequiresCondition: damaged && !being-warped
		TargetTypes: Repair
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	HiddenUnderFog:
	AttackMove:
	ActorLostNotification:
		Notification: NavalUnitLost
		TextNotification: Naval unit lost.
	RepairableNear:
		RepairActors: syrd, spen, syrd.gdi, spen.nod
	WithDamageOverlay:
	FireWarheadsOnDeath:
		Weapon: UnitExplodeShip
		EmptyWeapon: UnitExplodeShip
		RequiresCondition: !being-warped
	Guard:
	Guardable:
	Tooltip:
		GenericName: Ship
	MustBeDestroyed:
	MapEditorData:
		ExcludeTilesets: INTERIOR
		Categories: Naval
	Voiced:
		VoiceSet: VehicleVoice
	WithFacingSpriteBody:
	HitShape:
	GrantConditionOnPrerequisite@ROF:
		Condition: revealonfire
		Prerequisites: global.revealonfire
	RevealOnFire:
		RevealGeneratedShroud: True
		RequiresCondition: revealonfire
	RevealOnDeath:
		Duration: 100
		Radius: 2c512
	DamagedByTintedCells@RADSTRONG:
		Damage: 100
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.strong
	DamagedByTintedCells@RADMED:
		Damage: 40
		DamageInterval: 16
		DamageTypes: RadiationDeath
		LayerName: radioactivity.medium
	GrantConditionOnTerrain@UNDERBRIDGE:
		Condition: under-bridge
		TerrainTypes: Bridge, Tunnel
	Cloak@UNDERBRIDGE:
		DetectionTypes: Underbridge
		UncloakOn: Unload
		InitialDelay: 0
		CloakDelay: 0
		CloakStyle: Palette
		CloakedPalette: submerged
		RequiresCondition: under-bridge
	DamageMultiplier@UNDERBRIDGE:
		RequiresCondition: under-bridge
		Modifier: 1
	Targetable@UNDERBRIDGE:
		RequiresCondition: under-bridge
		TargetTypes: Underbridge
	DetectCloaked@UNDERBRIDGE:
		DetectionTypes: Underbridge
		Range: 5c0
	GrantConditionOnBotOwner@IAMBOT:
		Condition: botowner
		Bots: brutal, vhard, hard, normal, easy, naval
	Wanders@NOIDLENAVY:
		WanderMoveRadius: 3
		RequiresCondition: (botowner && under-bridge)
	GpsRadarDot:
		Sequence: Ship

^Submarine:
	Inherits@1: ^Ship
	Targetable:
		TargetTypes: Ground, Water, Ship, Submarine
		RequiresCondition: !underwater && !being-warped
	Targetable@UNDERWATER:
		TargetTypes: Underwater, Submarine
		RequiresCondition: underwater && !being-warped
	GrantConditionOnTerrain@CROSSINGFORD:
		Condition: crossing-ford
		TerrainTypes: Ford
	Cloak:
		DetectionTypes: Underwater
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Heal
		InitialDelay: 0
		CloakDelay: 50
		CloakSound: subshow1.aud
		UncloakSound: subshow1.aud
		CloakedCondition: underwater
		Palette: submerged
		PauseOnCondition: cloak-force-disabled || invisibility || being-warped || crossing-ford
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
		Color: 00ff0020
		BorderColor: 00000020
	FireWarheadsOnDeath:
		Weapon: UnitExplodeSubmarine
		EmptyWeapon: UnitExplodeSubmarine
		RequiresCondition: !under-bridge && !being-warped
	-MustBeDestroyed:
	Wanders@NOIDLENAVY:
		RequiresCondition: (botowner && crossing-ford) ||  (botowner && under-bridge)
	-Targetable@MINIDRONE:
	-ChangesHealth@MINIDRONE:
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || underwater
		ColorSource: Player
	-AttachableTo@MINIDRONE:
	Targetable@WatcherParasite:
		RequiresCondition: !underwater && !observer-attached

^NeutralPlane:
	Inherits@1: ^ExistsInWorld
	Inherits@3: ^IronCurtainable
	Inherits@4: ^SpriteActor
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableCombatUnit
	Inherits@handicaps: ^PlayerHandicaps
	OwnerLostAction:
		Action: Kill
	Armor:
		Type: Aircraft
	UpdatesPlayerStatistics:
	AppearsOnRadar:
		UseLocation: true
	Selectable:
		Bounds: 1024, 1024
	Aircraft:
		AirborneCondition: airborne
		TakeOffOnResupply: true
	Targetable@GROUND:
		RequiresCondition: !airborne
		TargetTypes: Ground, Vehicle
	Targetable@AIRBORNE:
		RequiresCondition: airborne
		TargetTypes: Air
	Targetable@REPAIR:
		RequiresCondition: !airborne && damaged
		TargetTypes: Repair
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	HiddenUnderFog:
		Type: GroundPosition
	AttackMove:
	Guard:
	Guardable:
	ActorLostNotification:
		Notification: AirUnitLost
		TextNotification: Airborne unit lost.
	Tooltip:
		GenericName: Plane
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
	WithFacingSpriteBody:
	MustBeDestroyed:
	Voiced:
		VoiceSet: GenericVoice
	HitShape:
	MapEditorData:
		Categories: Aircraft
	SpawnActorOnDeath:
		RequiresCondition: airborne
	FireWarheadsOnDeath:
		Weapon: UnitExplode
		RequiresCondition: !airborne
	RevealOnFireCA:
		RevealGeneratedShroud: True
		Duration: 20
		Radius: 2c0
		GroundPosition: true
	RevealOnDeath:
		Duration: 100
		Radius: 2c512
	Contrail@1:
		StartColorUsePlayerColor: false
		ZOffset: -512
		StartColor: FFFFFF80
		StartColorAlpha: 96
	Contrail@2:
		StartColorUsePlayerColor: false
		ZOffset: -512
		StartColor: FFFFFF80
		StartColorAlpha: 96
	GpsRadarDot:
		Sequence: Plane
	SpeedCapSpeedMultiplier@IRONCURTAIN:
		MaxSpeed: 112

^Plane:
	Inherits: ^NeutralPlane
	Inherits@2: ^GainsExperience
	Inherits@3: ^Cloakable
	Inherits@4: ^EmpDisable
	Inherits@5: ^GDIUpgradeAircraft
	Inherits@8: ^Warpable
	Inherits@9: ^BotAirProductionBonus
	Inherits@10: ^Chillable
	Inherits@11: ^Suppressable
	Inherits@12: ^Veilable
	Inherits@CRATE-CLOAK: ^CrateCloak
	Inherits@SovietT4Boost: ^SovietT4Boost
	Inherits@oilcost: ^OilRefCostReduction
	Inherits@C4: ^C4Plantable
	Inherits@TNT: ^TNTPlantable
	Inherits@ionsurge: ^IonSurgable
	Inherits@atomize: ^Atomizable
	Inherits@Blindable: ^Blindable
	Inherits@DevelopmentPolicyVeterancyBonus: ^DevelopmentPolicyVeterancyBonus
	-GrantConditionOnPrerequisite@MunitionsPlantBoost:
	-ReloadDelayMultiplier@MunitionsPlantBoost:
	Huntable:
	EjectOnDeath:
		PilotActor: E1
		SuccessRate: 50
		EjectOnGround: false
		EjectInAir: true
		AllowUnsuitableCell: true
		ChuteSound: chute1.aud
	Cloak@TIBSTEALTH:
		RequiresCondition: invisibility || crate-cloak && (!cloak-force-disabled && !airborne && !being-warped)
		CloakDelay: 90
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Damage, Heal
	Repairable:
		RepairActors: fix, rep, srep, afld, afld.gdi
	Aircraft:
		CruisingCondition: cruising
		PauseOnCondition: empdisable && !airborne
	KillsSelf@Emp:
		RequiresCondition: empdisable && cruising
	Targetable@GROUND:
		RequiresCondition: !airborne && !being-warped
		TargetTypes: Ground, Vehicle
	Targetable@C4Plantable:
		RequiresCondition: !airborne && !being-warped
	Targetable@TNTPlantable:
		RequiresCondition: !airborne && !being-warped
	Targetable@AIRBORNE:
		RequiresCondition: airborne && !being-warped
	Targetable@REPAIR:
		RequiresCondition: !airborne && damaged && !being-warped
	Targetable@C4Attached:
		RequiresCondition: c4 && !airborne
	Targetable@TNTAttached:
		RequiresCondition: tnt && !airborne
	ProductionCostMultiplier@EagleBonus:
		Multiplier: 90
		Prerequisites: player.eagle
	FireWarheadsOnDeath:
		RequiresCondition: !airborne && !being-warped
	Targetable@TEMPORAL:
		TargetTypes: TemporalAir
	-SpeedMultiplier@chilled2:
	-SpeedMultiplier@chilled3:
	-SpeedMultiplier@chilled4:
	-SpeedMultiplier@chilled5:
	-SpeedMultiplier@chilled6:
	-SpeedMultiplier@chilled7:
	-SpeedMultiplier@chilled8:
	-SpeedMultiplier@chilled9:
	-SpeedMultiplier@chilled10:
	WithShadow:
		RequiresCondition: !invisibility
	SoundOnDamageTransitionCA:
		DestroyedSounds: vrapdiea.aud, vrapdieb.aud
		RequiresCondition: airborne
	ExternalCondition@UNITSELL:
		Condition: unit-sellable
	Sellable:
		RequiresCondition: unit-sellable && !c4 && !being-warped
		SellSounds: cashturntd.aud
		Notification: UnitSold
		TextNotification: Unit sold.
		Cursor: sell2

^PlaneTD:
	Inherits@1: ^Plane
	Inherits@TDPAL: ^TDPalette
	EjectOnDeath:
		PilotActor: N1

^Helicopter:
	Inherits: ^Plane
	Tooltip:
		GenericName: Helicopter
	Aircraft:
		CanHover: True
		WaitDistanceFromResupplyBase: 4c0
		VTOL: true
		InitialFacing: 896
		CanSlide: True
		LandableTerrainTypes: Clear,Road,Rough,Ore,Gems,Tiberium,BlueTiberium,BlackTiberium,Beach,Bridge,Tunnel,Ford
		Crushes: crate, mine, infantry, husk
	Repairable:
		RepairActors: fix, rep, srep, hpad, hpad.td, afld, afld.gdi, grav
	Hovers@CRUISING:
		RequiresCondition: cruising && !being-warped
	-Contrail@1:
	-Contrail@2:
	GpsRadarDot:
		Sequence: Helicopter
	SoundOnDamageTransitionCA:
		DestroyedSounds: vhelidi1a.aud, vhelidi2a.aud
		RequiresCondition: airborne

^HelicopterTD:
	Inherits@1: ^Helicopter
	Inherits@TDPAL: ^TDPalette
	EjectOnDeath:
		PilotActor: N1

^Barrel:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@shape: ^1x1Shape
	Interactable:
		Bounds: 1024, 1024
	Health:
		HP: 1000
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
		Type: Footprint
	Tooltip:
		Name: Explosive Barrel
		ShowOwnerRow: False
	Armor:
		Type: None
	Targetable:
		TargetTypes: Ground, Barrel, NoAutoTarget
	MapEditorData:
		Categories: Decoration
	Building:
		Dimensions: 1,1
		Footprint: x
		TerrainTypes: Clear,Road
	WithSpriteBody:
	FrozenUnderFog:
	FrozenUnderFogUpdatedByGpsRadar:

^BasicBuilding:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^IronCurtainable
	Inherits@3: ^SpriteActor
	Inherits@C4Plantable: ^C4Plantable
	Inherits@TNTPlantable: ^TNTPlantable
	Inherits@Chillable: ^Chillable
	Inherits@shape: ^1x1Shape
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableBuilding
	Inherits@handicaps: ^PlayerHandicaps
	Targetable:
		TargetTypes: Ground, Structure, Building
	Building:
		Dimensions: 1,1
		Footprint: x
		TerrainTypes: Clear,Road
		RequiresBaseProvider: True
		BuildSounds: placbldg.aud, build5.aud
		UndeploySounds: cashturn.aud
	ActorPreviewPlaceBuildingPreview:
	RequiresBuildableArea:
		AreaTypes: building
		Adjacent: 4
	SoundOnDamageTransition:
		DamagedSounds: kaboom1.aud, xplobig4.aud
		DestroyedSounds: kaboom22.aud, crumble.aud, xplobig4.aud
	WithSpriteBody:
	FireWarheadsOnDeath:
		Type: Footprint
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode
	CaptureNotification:
		TextNotification: Structure captured.
		LoseNotification: OurBuildingCaptured
		LoseTextNotification: Structure lost (captured).
	ShakeOnDeath:
	Guardable:
		Range: 3c0
	FrozenUnderFog:
	FrozenUnderFogUpdatedByGpsRadar:
	EdibleByLeap:
	Tooltip:
		GenericName: Structure
	MapEditorData:
		Categories: Building
	CommandBarBlacklist:
	WithColoredOverlay@FLARE:
		Color: ff000080
		RequiresCondition: flare
	ExternalCondition@FLARE:
		Condition: flare
	WithDecoration@tnt:
		Sequence: tntbig
	GivesExperienceCA:
		ActorExperienceModifier: 5000

^Building:
	Inherits@1: ^BasicBuilding
	Inherits@2: ^Cloakable
	Inherits@3: ^ForceShieldable
	Inherits@4: ^EmpDisable
	Inherits@6: ^WarpableBuilding
	Inherits@7: ^BotBuilding
	Inherits@HACKABLE: ^Hackable
	Inherits@WatcherParasite: ^WatcherParasiteAttachable
	Inherits@TargetPaintable: ^TargetPaintable
	Inherits@DefensePolicyDamageReduction: ^DefensePolicyDamageReduction
	WithPalettedOverlay@IRONCURTAIN:
		RequiresCondition: invulnerability && !forceshield
		Palette: invuln
	Huntable:
	OwnerLostAction:
		Action: Kill
	UpdatesPlayerStatistics:
	GivesBuildableArea:
		AreaTypes: building, fake
	RepairableBuilding:
		RepairStep: 500
		RepairPercent: 30
		RepairingNotification: Repairing
		RequiresCondition: !being-warped
		RepairCondition: being-repaired
	InstantlyRepairable:
		RequiresCondition: !tnt && !c4
	AcceptsDeliveredCash:
	WithMakeAnimation:
		Condition: build-incomplete
	CaptureManager:
		BeingCapturedCondition: being-captured
	Capturable:
		RequiresCondition: !build-incomplete && !being-warped
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	SpawnActorsOnSellCA:
		ActorTypes: e1,e1,e1,e1,e1,e1,e1,e1,e1,e1,c1,c1,c1,c1,c7,c7,c7,c7,c10,c10
		GuaranteedActorTypes: e1, e1
		GuaranteedActorsLimitedByValue: true
	SpawnActorOnDeath:
		Actor: e1
		Probability: 5
		RequiresCondition: !being-warped && !hacked
	SpawnRandomActorOnDeath:
		Actors: c1,c7,c10
		Probability: 10
		RequiresCondition: !being-warped && !hacked
	MustBeDestroyed:
		RequiredForShortGame: true
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-captured && !being-warped && !hacked
		SellSounds: cashturn.aud
		Notification: StructureSold
		TextNotification: Structure sold.
	WithBuildingRepairDecoration:
		Image: allyrepair
		Sequence: repair
		Position: Center
		Palette: player
		IsPlayerPalette: True
		RequiresCondition: !being-warped
	Targetable:
		RequiresCondition: !being-warped
	Targetable@C4Plantable:
		RequiresCondition: !being-warped
	Targetable@TNTPlantable:
		RequiresCondition: !being-warped
	Targetable@HACKCAPTURABLE:
		TargetTypes: HackCapturable
		RequiresCondition: !build-incomplete && !being-warped
	FireWarheadsOnDeath:
		RequiresCondition: !being-warped
	FireWarheadsOnDeath@TEMPORAL:
		Weapon: TemporalExplodeLarge
		EmptyWeapon: TemporalExplodeLarge
	GpsRadarDot:
		Sequence: Structure
	DelayedWeaponAttachable@C4:
		RequiresCondition: !invulnerability && !forceshield
	DelayedWeaponAttachable@C4Seal:
		RequiresCondition: !invulnerability && !forceshield
	DelayedWeaponAttachable@TNT:
		RequiresCondition: !invulnerability && !forceshield
	Cloak@TIBSTEALTH:
		CloakSound: cloak5.aud
		UncloakSound: appear1.aud
	Cloak@SGENCLOAK:
		CloakSound: cloak5.aud
		UncloakSound: appear1.aud
	UpdatesBuildOrder:
	UpdatesKillCount@BuildingsOrHarvesters:
		Type: BuildingsOrHarvesters
	InfiltrateToCreateProxyActor@InfiltratorVision:
		Proxy: camera.infiltrator
		Types: VisionInfiltrate
		UseLocation: true
		UseCenterPosition: true
		LinkedToParent: true
	InfiltrateForTimedCondition@InfiltratorVisionIcon:
		Condition: observer-attached
		Types: VisionInfiltrate
		Duration: 3000
	Targetable@InfiltratorVision:
		TargetTypes: VisionInfiltrate
		RequiresCondition: !being-warped
	GivesPlayerExperienceOnCapture:
		CaptureTypes: building
		PlayerExperience: 12
		AddExperienceFromValue: true
		ValuePercentage: 1
		PlayerExperienceRelationships: Enemy, Neutral
		SubsequentCaptureModifier: 50

^BuildingTD:
	Inherits@1: ^Building
	Inherits@TDPAL: ^TDPalette
	SpawnActorOnDeath:
		Actor: n1
	SpawnActorsOnSellCA:
		ActorTypes: n1,n1,n1,n1,n1,n1,n1,n1,n1,n1,c1,c1,c1,c1,c7,c7,c7,c7,c10,c10
		GuaranteedActorTypes: n1, n1
	ActorPreviewPlaceBuildingPreview:
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
		DeathSequencePalette: playertd

^WaterStructure:
	Targetable@WATER:
		TargetTypes: Water, WaterStructure

^Defense:
	Inherits: ^Building
	Inherits@botbonus: ^BotDefenseProductionBonus
	Inherits@Concussion: ^Concussion
	Inherits@selection: ^SelectableCombatBuilding
	Inherits@veilable: ^Veilable
	Inherits@weapjammable: ^WeaponJammable
	Inherits@atomize: ^Atomizable
	Inherits@DefensePolicyDamageBonus: ^DefensePolicyDamageBonus
	-SpeedMultiplier@concussion:
	WithPalettedOverlay@IRONCURTAIN:
		RequiresCondition: invulnerability
		Palette: invuln
	Selectable:
		Bounds: 1024, 1024
	Targetable:
		TargetTypes: Ground, Structure, Defense
	DetectCloaked:
		DetectionTypes: Cloak, AirCloak
		Range: 6c0
		RequiresCondition: !(empdisable || being-warped)
	MustBeDestroyed:
		RequiredForShortGame: false
	-GivesBuildableArea:
	-AcceptsDeliveredCash:
	-WithPalettedOverlay@FS:
	-DamageMultiplier@FS:
	-TimedConditionBar@FS:
	-ExternalCondition@FS:
	-PowerMultiplier@FSPOWERDOWN:
	-CaptureManager:
	-Capturable:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-SpawnActorsOnSellCA:
	-InfiltrateToCreateProxyActor@InfiltratorVision:
	RepairableBuilding:
		RepairStep: 700
		-RepairPercent:
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped && !hacked
	RenderRangeCircle:
		RangeCircleType: DefenseRange
		Color: C12900
	FireWarheadsOnDeath:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	MapEditorData:
		Categories: Defense
	-CommandBarBlacklist:
	RevealOnDeath:
		Radius: 2c768
	GrantConditionOnPrerequisite@ROF:
		Condition: revealonfire
		Prerequisites: global.revealonfire
	RevealOnFire:
		RevealGeneratedShroud: True
		RequiresCondition: revealonfire
	ProductionCostMultiplier@FranceBonus:
		Multiplier: 90
		Prerequisites: player.france
	FireWarheadsOnDeath@TEMPORAL:
		Weapon: TemporalExplode
		EmptyWeapon: TemporalExplode
	DetectCloaked@UNDERBRIDGE:
		DetectionTypes: Underbridge
		Range: 6c0
	-Targetable@HACKCAPTURABLE:
	DelayedWeaponAttachable@C4:
		RequiresCondition: !invulnerability
	DelayedWeaponAttachable@C4Seal:
		RequiresCondition: !invulnerability
	DelayedWeaponAttachable@TNT:
		RequiresCondition: !invulnerability
	RequiresBuildableArea:
		AreaTypes: building, defense
	GivesExperienceCA:
		ActorExperienceModifier: 10000
	-UpdatesBuildOrder:
	ChangesHealth@DefensePolicy2:
		Step: 140

^DefenseTD:
	Inherits@1: ^Defense
	Inherits@TDPAL: ^TDPalette
	ActorPreviewPlaceBuildingPreview:

^AntiAirDefense:
	Targetable@AntiAirDefense:
		TargetTypes: AntiAirDefense

^BuildingPlug:
	Interactable:
	AlwaysVisible:
	Building:
		BuildSounds: placbldg.aud
		UndeploySounds: cashturn.aud
	SequencePlaceBuildingPreview:
		Sequence: place
		Palette: terrain
	KillsSelf:
		RemoveInstead: true
	RenderSprites:

^Wall:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@shape: ^1x1Shape
	CustomRadarColor:
		Color: EEEEEE
	Inherits@3: ^Cloakable
	Inherits@handicaps: ^PlayerHandicaps
	Interactable:
		Bounds: 1024, 1024
	OwnerLostAction:
		Action: ChangeOwner
	Building:
		Dimensions: 1,1
		Footprint: x
		BuildSounds: placbldg.aud
		TerrainTypes: Clear,Road
		UndeploySounds: cashturn.aud
	FootprintPlaceBuildingPreview:
	RequiresBuildableArea:
		AreaTypes: building, defense
		Adjacent: 7
	SoundOnDamageTransition:
		DamagedSounds: sandbag2.aud
		DestroyedSounds: sandbag2.aud
	Crushable:
		CrushClasses: wall
	LineBuild:
		Range: 8
		NodeTypes: wall
	LineBuildNode:
		Types: wall
	Targetable:
		TargetTypes: Ground, Wall, NoAutoTarget
	-GivesExperienceCA:
	WithWallSpriteBody:
	Sellable:
		SellSounds: cashturn.aud
	Guardable:
	FrozenUnderFog:
	Health:
		HP: 10000
	AppearsOnMapPreview:
		Terrain: Wall
	MapEditorData:
		Categories: Wall
	ProductionCostMultiplier@FranceBonus:
		Multiplier: 90
		Prerequisites: player.france
	GrantExternalConditionToCrusher@CRUSHATTEMPTSLOW:
		WarnCrushCondition: crush-attempt-slow
		WarnCrushDuration: 50
		OnCrushCondition: crush-attempt-slow
		OnCrushDuration: 100
	GrantExternalConditionToCrusher@CRUSHSLOW:
		OnCrushCondition: crush-slow
		OnCrushDuration: 50

^TechBuilding:
	Inherits: ^BasicBuilding
	Inherits@Cloakable: ^Cloakable
	Inherits@Hackable: ^Hackable
	Inherits@WatcherParasite: ^WatcherParasiteAttachable
	Inherits@TargetPaintable: ^TargetPaintable
	Huntable:
	Health:
		HP: 40000
	Armor:
		Type: Wood
	Tooltip:
		Name: Civilian Building
		GenericVisibility: None
	MapEditorData:
		Categories: Tech building
	GivesPlayerExperienceOnCapture:
		CaptureTypes: building
		PlayerExperience: 10
		PlayerExperienceRelationships: Enemy, Neutral
		SubsequentCaptureModifier: 50

^CommandoSkull:
	WithDecoration@COMMANDOSKULL:
		Image: pips
		Sequence: pip-skull
		Palette: effect
		Position: TopLeft
		ValidRelationships: Ally, Enemy, Neutral

^CommandoRegen:
	ChangesHealth@CommandoRegen:
		PercentageStep: 5
		Delay: 100
		StartIfBelow: 100
		DamageCooldown: 150
		RequiresCondition: !rank-elite

^FakeBuilding:
	Inherits: ^Building
	GivesBuildableArea:
		AreaTypes: fake
	RequiresBuildableArea:
		AreaTypes: fake
	Health:
		HP: 10000
	RevealsShroud:
		Range: 1c0
	SoundOnDamageTransition:
		-DamagedSounds:
	WithDecoration@fake:
		RequiresSelection: true
		Image: pips
		Palette: chrome
		Sequence: tag-fake
		Position: Top
		Margin: 0, 4
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-MustBeDestroyed:
	MapEditorData:
		Categories: Fake
	TooltipExtras:
		Attributes: • Can be detonated remotely
	FireWarheadsOnDeath@FAKE:
		Weapon: FakeBuildingExplode
		DamageThreshold: 70
		Type: CenterPosition
	GrantConditionOnDeploy:
		DeployedCondition: detonate
		SkipMakeAnimation: true
		DeployCursor: c4
		RequiresCondition: !detonate-disabled
	WithFlashEffect@Detonate:
		Color: ff0000
		Interval: 5000
		RequiresCondition: detonate
	PeriodicExplosion:
		Weapon: FakeBuildingSelfDestruct
		RequiresCondition: detonate
	GrantTimedCondition@DETONATEDELAY:
		Condition: detonate-disabled
		Duration: 125
	-UpdatesKillCount@BuildingsOrHarvesters:
	-InfiltrateToCreateProxyActor@InfiltratorVision:
	-Targetable@InfiltratorVision:

^InfiltratableFake:
	Targetable@Infiltration:
		TargetTypes: PowerOutageInfiltrate, VisionInfiltrate
	InfiltrateForDecoration:
		Types: PowerOutageInfiltrate, VisionInfiltrate, StealCreditsInfiltrate, VetInfiltrate, ResetShroudInfiltrate, GrantSupportPowerInfiltrate
		RequiresSelection: true
		Image: pips
		Palette: chrome
		Sequence: tag-fake
		Position: Top
		Margin: 0, 4

^AmmoBox:
	Inherits: ^BasicBuilding
	-Selectable:
	Huntable:
	Health:
		HP: 1000
	FireWarheadsOnDeath:
		Weapon: UnitExplode
	Tooltip:
		Name: Ammo Box
		GenericVisibility: None
	Targetable:
		TargetTypes: Ground, Structure, NoAutoTarget
	Armor:
		Type: Light
	MapEditorData:
		Categories: Decoration
	Interactable:
		Bounds: 1024, 1024

^CivBuilding:
	Inherits: ^BasicBuilding
	Huntable:
	Health:
		HP: 40000
	Armor:
		Type: Wood
	Tooltip:
		Name: Civilian Building
		GenericVisibility: None
	RenderSprites:
		Palette: player
	MapEditorData:
		ExcludeTilesets: INTERIOR
		Categories: Civilian building
	SpawnRandomActorOnDeath@1:
		Actors: c1,c2,c3,c4
		Probability: 40
	SpawnRandomActorOnDeath@2:
		Actors: c10,c4,c5,c6
		Probability: 20
	SpawnRandomActorOnDeath@3:
		Actors: c5,c7,c8,c9
		Probability: 15
	FireWarheadsOnDeath:
		Weapon: SmallBuildingExplode
	FireWarheadsOnDeath@CIVPANIC:
		Weapon: CivPanicExplosion

^CivField:
	Inherits: ^CivBuilding
	-HitShape:
	-Health:
	-FireWarheadsOnDeath:
	-FireWarheadsOnDeath@CIVPANIC:
	-Selectable:
	-SelectionDecorations:
	Tooltip:
		Name: Field
	-Targetable:
	Interactable:

^ResourceNode:
	SeedsResourceCA:
		RequiresCondition: !voidspike
	GrantConditionOnPrerequisite@FastRegrowth:
		Condition: fast-regrowth
		Prerequisites: global.fastregrowth
	SeedsResourceMultiplier@FastRegrowth:
		Modifier: 50
		RequiresCondition: fast-regrowth
	SeedsResourceMultiplier@IchorBoost:
		Modifier: 80
		RequiresCondition: ichor-boost
	ExternalCondition@IchorBoost:
		Condition: ichor-boost
	ExternalCondition@VoidSpike:
		Condition: voidspike
	WithIdleOverlay@IchorBoost:
		Image: resconv
		Sequence: green
		Palette: scrin
		RequiresCondition: ichor-boost
		Offset: 0,0,300
	SeedsResource@BlackTiberium:
		ResourceType: BlackTiberium
		Interval: 66
		RequiresCondition: voidspike
	WithColoredOverlay@VoidSpike:
		Color: 00000070
		RequiresCondition: voidspike

^TibTree:
	Inherits@1: ^SpriteActor
	Inherits@ResourceNode: ^ResourceNode
	Interactable:
	Tooltip:
		Name: Blossom Tree
		ShowOwnerRow: false
	RenderSprites:
		Palette: temptd
	WithSpriteBody:
	WithIdleAnimation:
		Interval: 100, 200
	Building:
		Footprint: x
		Dimensions: 1,1
	AppearsOnRadar:
	RadarColorFromTerrain:
		Terrain: Tiberium
	HiddenUnderShroud:
	WithMakeAnimation:
	MapEditorData:
		Categories: Resource spawn
	ProximityExternalCondition@ONTIB:
		Condition: on-tib
		Range: 4c0
		ValidRelationships: Ally, Neutral, Enemy

^Tree:
	Inherits@1: ^SpriteActor
	Inherits@shape: ^1x1Shape
	Interactable:
	Tooltip:
		Name: Tree
		ShowOwnerRow: false
	RenderSprites:
		Palette: terrain
	WithSpriteBody:
	Building:
		Footprint: x
		Dimensions: 1,1
	AppearsOnRadar:
	RadarColorFromTerrain:
		Terrain: Tree
	AppearsOnMapPreview:
		Terrain: Tree
	Health:
		HP: 50000
	Armor:
		Type: Tree
	Targetable:
		TargetTypes: Trees
	WithDamageOverlay@SmallBurn:
		DamageTypes: Incendiary
		Image: burn-s
		Palette: effect
		MinimumDamageState: Light
		MaximumDamageState: Medium
	WithDamageOverlay@MediumBurn:
		DamageTypes: Incendiary
		Image: burn-m
		Palette: effect
		MinimumDamageState: Medium
		MaximumDamageState: Heavy
	WithDamageOverlay@LargeBurn:
		DamageTypes: Incendiary
		Image: burn-l
		Palette: effect
		MinimumDamageState: Heavy
		MaximumDamageState: Dead
	HiddenUnderShroud:
	ScriptTriggers:
	MapEditorData:
		ExcludeTilesets: INTERIOR
		Categories: Tree
	RequiresSpecificOwners:
		ValidOwnerNames: Neutral

^TreeHusk:
	Inherits@1: ^SpriteActor
	Interactable:
	RenderSprites:
		Palette: terrain
	AppearsOnRadar:
	RadarColorFromTerrain:
		Terrain: Tree
	AppearsOnMapPreview:
		Terrain: Tree
	Building:
		Footprint: x
		Dimensions: 1,1
	WithSpriteBody:
	Tooltip:
		Name: Tree (Burnt)
		ShowOwnerRow: false
	HiddenUnderShroud:
	ScriptTriggers:
	MapEditorData:
		Categories: Tree
	RequiresSpecificOwners:
		ValidOwnerNames: Neutral

^Box:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		-ExcludeTilesets:
		Categories: Decoration

^BasicHusk:
	Inherits@1: ^SpriteActor
	Interactable:
	Health:
		HP: 28000
	Armor:
		Type: Heavy
	HiddenUnderFog:
		Type: CenterPosition
		AlwaysVisibleRelationships: None
	ScriptTriggers:
	WithFacingSpriteBody:
	HitShape:
	MapEditorData:
		Categories: Husk

^Husk:
	Inherits: ^BasicHusk
	Inherits@chrono: ^Chronoshiftable
	Husk:
		AllowedTerrain: Clear,Road,Rough,Ore,Gems,Tiberium,BlueTiberium,BlackTiberium,Beach,Bridge,Tunnel,Ford
	WithIdleOverlay@Burns:
		Image: fire
		Sequence: 1
		IsDecoration: true
		Palette: effect
		RequiresCondition: !being-captured
	ChangesHealth:
		PercentageStep: -10
		StartIfBelow: 101
		Delay: 150
		RequiresCondition: !being-captured
	OwnerLostAction:
		Action: ChangeOwner
	CaptureManager:
		BeingCapturedCondition: being-captured
	Capturable:
		Types: husk
		ValidRelationships: Ally, Enemy, Neutral
	CapturableProgressBar:
	CapturableProgressBlink:
	GivesCashOnCaptureCA:
		Amount: 200
		SuffixToRemoveForValueActor: .husk
		ValueActorPercentage: 30
	GrantConditionOnCapture:
		Condition: salvaged
	KillsSelf:
		RemoveInstead: True
		RequiresCondition: salvaged
	WithColoredOverlay@IDISABLE:
		Color: 000000b4
	Targetable:
		TargetTypes: Ground, Husk, NoAutoTarget
		RequiresForceFire: true
	Tooltip:
		GenericName: Destroyed Vehicle
	FireWarheadsOnDeath:
		Weapon: VisualExplodeHusk
		EmptyWeapon: VisualExplodeHusk
		DamageSource: Killer
	Crushable:
		CrushClasses: wall
		CrushSound: destw.aud
		CrushedByFriendlies: True
	ChronoshiftableCA:
		ReturnToAvoidDeath: false
		-RequiresCondition:
		-Condition:
	-HealthCapDamageMultiplier@CHRONO:

^HuskTD:
	Inherits: ^Husk
	Inherits@TDPAL: ^TDPalette
	WithIdleOverlay@Burns:
		Image: fire
		Sequence: 5
		Palette: tdeffect

^PlaneHusk:
	Inherits: ^BasicHusk
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
	Tooltip:
		GenericName: Destroyed Plane
	Aircraft:
	FallsToEarth:
		MaximumSpinSpeed: 0
		Moves: True
		Velocity: 70
		Explosion: UnitExplodePlane
	-MapEditorData:
	RevealOnDeath:
		Duration: 60
		Radius: 4c0
	Contrail@1:
		StartColorUsePlayerColor: false
		ZOffset: -512
		StartColor: FFFFFF80
		StartColorAlpha: 96
	Contrail@2:
		StartColorUsePlayerColor: false
		ZOffset: -512
		StartColor: FFFFFF80
		StartColorAlpha: 96

^PlaneHuskTD:
	Inherits: ^PlaneHusk
	Inherits@TDPAL: ^TDPalette

^PlaneHuskEmpty:
	FallsToEarth:
		Explosion: UnitExplodePlaneEmpty

^HelicopterHusk:
	Inherits: ^BasicHusk
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
	Tooltip:
		GenericName: Destroyed Helicopter
	Aircraft:
		CanHover: True
		VTOL: true
		CanSlide: True
	FallsToEarth:
		Explosion: UnitExplodeHeli
		MaximumSpinSpeed: 40
	-MapEditorData:
	RevealOnDeath:
		Duration: 60
		Radius: 4c0

^HelicopterHuskTD:
	Inherits: ^HelicopterHusk
	Inherits@TDPAL: ^TDPalette

^HelicopterHuskEmpty:
	FallsToEarth:
		Explosion: UnitExplodeHeliEmpty

^TechHusk:
	Inherits@1: ^BasicBuilding
	Health:
		HP: 2800000
	WithIdleOverlay@Burns:
		Image: fire
		Sequence: 1
		IsDecoration: true
		Palette: effect
	ChangesHealth:
		Step: -1000
		StartIfBelow: 101
		Delay: 8
	CaptureManager:
	Capturable:
		Types: building
		ValidRelationships: Enemy, Neutral, Ally
	CapturableProgressBar:
	CapturableProgressBlink:
	TransformOnCapture:
		ForceHealthPercentage: 25
	Tooltip:
		GenericName: Destroyed Building
	FireWarheadsOnDeath@NORMAL:
		Type: Footprint
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode

^Bridge:
	Inherits@shape: ^1x1Shape
	AlwaysVisible:
	Tooltip:
		Name: Bridge
		ShowOwnerRow: false
	Targetable:
		TargetTypes: Ground, Water, Bridge
		RequiresForceFire: true
	Building:
		Footprint: ____ ____
		Dimensions: 4,2
	Health:
		HP: 100000
	Armor:
		Type: Concrete
	ScriptTriggers:
	ClassicFacingBodyOrientation:
		QuantizedFacings: 1
	Interactable:
		Bounds: 4096, 2048
	ShakeOnDeath:
		Duration: 15
		Intensity: 6
	Bridge:
		DamageTypes: BulletDeath

^Rock:
	Inherits@1: ^SpriteActor
	Interactable:
	Tooltip:
		Name: Rock
		ShowOwnerRow: false
	RenderSprites:
		Palette: desert
	WithSpriteBody:
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	AppearsOnRadar:
	RadarColorFromTerrain:
		Terrain: Tree
	HiddenUnderShroud:
	MapEditorData:
		RequireTilesets: DESERT
		Categories: Decoration
	RequiresSpecificOwners:
		ValidOwnerNames: Neutral
	AppearsOnMapPreview:
		Terrain: Tree

^DesertCivBuilding:
	Inherits: ^CivBuilding
	RenderSprites:
		Palette: desert
	MapEditorData:
		RequireTilesets: DESERT

^Crate:
	Inherits@1: ^SpriteActor
	Interactable:
		Bounds: 1024, 1024
	HiddenUnderFog:
	Tooltip:
		Name: Crate
		GenericName: Crate
		ShowOwnerRow: false
	Crate:
		Duration: 4500
		TerrainTypes: Clear, Rough, Road, Ore, Beach, Tiberium, BlueTiberium, BlackTiberium, Water
	RenderSprites:
		Palette: effect
		Image: scrate
	WithCrateBody:
		XmasImages: xcratea, xcrateb, xcratec, xcrated
		LandSequence: land
		WaterSequence: water
	Parachutable:
		FallRate: 26
		KilledOnImpassableTerrain: false
		ParachutingCondition: parachute
	Passenger:
	WithParachute:
		Image: parach
		Sequence: idle
		OpeningSequence: open
		ShadowImage: parach-shadow
		ShadowSequence: idle
		RequiresCondition: parachute
	MapEditorData:
		Categories: System

^Mine:
	Inherits: ^SpriteActor
	Interactable:
		Bounds: 1024, 1024
	ScriptTriggers:
	WithSpriteBody:
	HiddenUnderFog:
	Mine:
		CrushClasses: mine
		DetonateClasses: mine
		AvoidFriendly: false
		BlockFriendly: false
	Health:
		HP: 10000
		NotifyAppliedDamage: false
	Cloak:
		CloakSound:
		UncloakSound:
		Palette:
		DetectionTypes: Mine
		InitialDelay: 0
	Tooltip:
		Name: Mine
	Targetable:
		TargetTypes: Ground, Mine
	Targetable@ONWATER:
		TargetTypes: Water
		RequiresCondition: onwater
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	Immobile:
		OccupiesSpace: true
	HitShape:
	MapEditorData:
		Categories: System
	GrantConditionOnTerrain@BADTERRAIN:
		Condition: badterrain
		TerrainTypes: Rock, River, Tree, Tunnel
	KillsSelf@BADTERRAIN:
		RequiresCondition: badterrain

^DisableOnLowPower:
	WithColoredOverlay@IDISABLE:
		RequiresCondition: disabled && !(invulnerability || empdisable || being-warped)
		Color: 000000b4
	GrantConditionOnPowerState@LOWPOWER:
		Condition: lowpower
		ValidPowerStates: Low, Critical
	GrantCondition@IDISABLE:
		RequiresCondition: lowpower || empdisable
		Condition: disabled

^DisableOnLowPowerOrForceDisabled:
	WithColoredOverlay@IDISABLE:
		RequiresCondition: disabled && !(invulnerability || empdisable || forceshield || being-warped)
		Color: 000000b4
	GrantConditionOnPowerState@LOWPOWER:
		Condition: lowpower
		ValidPowerStates: Low, Critical
	GrantCondition@IDISABLE:
		RequiresCondition: lowpower || empdisable || forcedisabled
		Condition: disabled
	ExternalCondition:
		Condition: forcedisabled
	TimedConditionBar@FSDISABLE:
		Condition: forcedisabled
		Color: FFFF66
	WithDecoration@FORCEDISABLED:
		Image: poweroff
		Sequence: offline
		Palette: chrome
		RequiresCondition: forcedisabled && !forceshield
		Position: Center

^DisableOnLowPowerOrPowerDown:
	Inherits: ^DisableOnLowPower
	GrantCondition@IDISABLE:
		RequiresCondition: lowpower || powerdown || empdisable
	ToggleConditionOnOrder:
		DisabledSound: EnablePower
		EnabledSound: DisablePower
		Condition: powerdown
		OrderName: PowerDown
	WithDecoration@POWERDOWN:
		Image: poweroff
		Sequence: offline
		Palette: chrome
		RequiresCondition: powerdown
		Position: Center
	PowerMultiplier@POWERDOWN:
		RequiresCondition: powerdown
		Modifier: 0
	RevealsShroudMultiplier@POWERDOWN:
		RequiresCondition: lowpower || powerdown || empdisable
		Modifier: 50
	WithBuildingRepairDecoration:
		Offsets:
			powerdown: -10, 0

^DisableOnLowPowerOrPowerDownOrForceDisable:
	Inherits: ^DisableOnLowPower
	GrantCondition@IDISABLE:
		RequiresCondition: lowpower || powerdown || empdisable || forcedisabled
	ToggleConditionOnOrder:
		DisabledSound: EnablePower
		EnabledSound: DisablePower
		Condition: powerdown
		OrderName: PowerDown
	WithDecoration@POWERDOWN:
		Image: poweroff
		Sequence: offline
		Palette: chrome
		RequiresCondition: powerdown || forcedisabled
		Position: Center
	PowerMultiplier@POWERDOWN:
		RequiresCondition: powerdown
		Modifier: 0
	PowerMultiplier@FORCEDISABLE:
		RequiresCondition: forcedisabled
		Modifier: 0
	WithBuildingRepairDecoration:
		Offsets:
			powerdown: -10, 0
	TimedConditionBar@FSDISABLE:
		Condition: forcedisabled
		Color: FFFF66
	ExternalCondition:
		Condition: forcedisabled

^DisabledByPowerOutage:
	WithColoredOverlay@IDISABLE:
		RequiresCondition: disabled && !forceshield && !invulnerability && !empdisable
		Color: 000000b4
	ExternalCondition:
		Condition: forcedisabled
	TimedConditionBar@FSDISABLE:
		Condition: forcedisabled
		Color: FFFF66
	WithDecoration@FSPOWERDOWN:
		Image: poweroff
		Sequence: offline
		Palette: chrome
		RequiresCondition: forcedisabled && !forceshield
		Position: Center
	GrantCondition@IDISABLE:
		RequiresCondition: power-outage || forcedisabled
		Condition: disabled
	AffectedByPowerOutage:
		Condition: power-outage
	Targetable@PowerOutageInfiltrate:
		TargetTypes: PowerOutageInfiltrate
		RequiresCondition: !being-warped
	InfiltrateForPowerOutage:
		Types: PowerOutageInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		PlayerExperience: 15
	PowerMultiplier@FORCEDISABLE:
		RequiresCondition: forcedisabled
		Modifier: 0
	Power:
		RequiresCondition: !disabled

^DisabledByRadarLoss:
	ExternalCondition@DRONECONTROL:
		Condition: radarenabled
	GrantConditionOnPrerequisite@DRONECONTROL:
		Condition: radarenabled
		Prerequisites: radar-active
	WithColoredOverlay@DRONEDISABLE:
		RequiresCondition: !radarenabled || empdisable
		Color: 000000b4
	WithDecoration@DRONEDISABLE:
		Image: nosignal
		Sequence: offline
		Palette: chrome
		RequiresCondition: !radarenabled || empdisable
		Position: Center

^Selectable:
	Selectable:
	SelectionDecorations:
	WithSpriteControlGroupDecoration:
	DrawLineToTarget:

^SelectableCombatUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 10
		PriorityModifiers: Ctrl

^SelectableSupportUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 8
		PriorityModifiers: Ctrl, Alt

^SelectableEconomicUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 6
		PriorityModifiers: Ctrl, Alt

^SelectableCombatBuilding:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 4

^SelectableBuilding:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 2

^EngineerBase:
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		Description: Utility infantry used for capturing/repairing structures.
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Captures enemy buildings\n• Repairs friendly structures & bridges\n• Defuses mines and bombs
	Valued:
		Cost: 400
	Passenger:
		CustomPipType: yellow
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	InstantlyRepairs:
	RepairsBridges:
		EnterBehaviour: Exit
	CaptureManager:
		CapturingCondition: capturing
	GrantCondition@CapturingDecloak:
		Condition: cloak-force-disabled
		RequiresCondition: capturing
	Captures:
		CaptureTypes: building
		CaptureDelay: 150
	Armament@bombdefuser:
		Weapon: DefuseKit
		Cursor: goldwrench
		OutsideRangeCursor: goldwrench
		TargetRelationships: Ally
		ForceTargetRelationships: None
	Armament@minedefuser:
		Weapon: MineDefuser
		Cursor: goldwrench
		OutsideRangeCursor: goldwrench
		PauseOnCondition: !ammo
		Name: secondary
	Armament@minedefusercharge:
		Weapon: MineDefuserCharger
		Cursor: goldwrench
		OutsideRangeCursor: goldwrench
		Name: tertiary
	AmmoPool@minedefuser:
		Ammo: 1
		InitialAmmo: 0
		AmmoCondition: ammo
		Armaments: secondary
	ReloadAmmoPoolCA@minedefuser:
		Delay: 50
		Count: 1
		RequiresCondition: charging
		ShowSelectionBar: true
		SelectionBarColor: ffff00
	GrantConditionOnAttack@CHARGING:
		Condition: charging
		ArmamentNames: tertiary
		RevokeDelay: 7
	AutoTarget:
		ScanRadius: 2
	AutoTargetPriority@defuse:
		ValidTargets: C4Attached, TNTAttached
		InvalidTargets: NoAutoTarget
		ValidRelationships: Ally
	AutoTargetPriority@mines:
		ValidTargets: Mine
		ValidRelationships: Enemy, Neutral
	AttackFrontal:
		Armaments: primary, secondary, tertiary
		PauseOnCondition: being-warped
		FacingTolerance: 0
	DetectCloaked:
		Range: 5c0
		DetectionTypes: Mine
	MineImmune:

^Viceroid:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@TDPAL: ^TDPalette
	Huntable:
	Health:
		HP: 10000
	Armor:
		Type: Light
	RevealsShroud:
		Range: 6c0
	Mobile:
		Voice: Move
		Speed: 60
		Locomotor: foot
	SelectionDecorations:
	WithSpriteControlGroupDecoration:
	Selectable:
		Bounds: 1024, 1024
	Targetable:
		TargetTypes: Ground, Infantry, Creep
	AutoTarget:
		ScanRadius: 5
	AttackMove:
		Voice: Attack
	DrawLineToTarget:
	HiddenUnderFog:
	Valued:
		Cost: 500
	Tooltip:
		Name: Visceroid
	Armament:
		Weapon: Chemspray
		LocalOffset: 384,0,0
		MuzzleSequence: muzzle
		MuzzlePalette: tdeffect-ignore-lighting-alpha85
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	ClassicFacingBodyOrientation:
		QuantizedFacings: 8
	WithSpriteBody:
	WithMuzzleOverlay:
	Guard:
		Voice: Move
	Guardable:
	DamagedByTerrain:
		Damage: -300
		DamageInterval: 50
		DamageTypes: ToxinDeath
		Terrain: Tiberium, BlueTiberium
	Voiced:
		VoiceSet: ViceVoice
	HitShape:
		Type: Circle
			Radius: 427
	MapEditorData:
		Categories: Critter

^ShootableMissile:
	Inherits@1: ^ExistsInWorld
	Inherits@4: ^SpriteActor
	Armor:
		Type: Aircraft
	Targetable:
		TargetTypes: ICBM
	HiddenUnderFog:
		Type: GroundPosition
	Tooltip:
		Name: Missile
		GenericName: Missile
		ShowOwnerRow: false
	HitShape:
	RejectsOrders:
	Interactable:
	WithFacingSpriteBody:
	WithShadow:

^ThrowsShrapnel:
	FireProjectilesOnDeath@small:
		Weapons: SmallDebris
		Pieces: 3, 7
		Range: 1c511, 3c0

^ThrowsShrapnelBig:
	Inherits: ^ThrowsShrapnel
	FireProjectilesOnDeath@fire:
		Weapons: FireDebris
		Pieces: 3, 7
		Range: 1c511, 3c0

^UnlocksMcvIfNoneOwned:
	ProvidesPrerequisite@allowsmcv:
		Prerequisite: vehicles.mcv
		RequiresCondition: !has-mcv && !has-conyard
	GrantConditionOnPrerequisiteCA@has-conyard:
		Condition: has-conyard
		Prerequisites: anyconyard
	GrantConditionOnPrerequisiteCA@has-mcv:
		Condition: has-mcv
		Prerequisites: anymcv

^AIUNLOAD:
	GrantConditionWhileAiming@AIUNLOAD:
		Condition: aiming
	GrantConditionOnDamageState@AIUNLOAD:
		Condition: damage
	UnloadOnCondition@AIUNLOAD:
		RequiresCondition: damage || aiming

^QueueUpdater:
	FreeActor@QUEUEUPDATER:
		Actor: QueueUpdaterDummy
		RequiresCondition: updatequeue
	SpawnActorOnCapture@QUEUEUPDATER:
		Actor: QueueUpdaterDummy
		Delay: 1
	GrantDelayedCondition@QUEUEUPDATER:
		Delay: 1
		Condition: updatequeue
	GrantConditionOnPrerequisite@MQF:
		Condition: global-multiqueue
		Prerequisites: global.multiqueuefull
	GrantConditionOnPrerequisite@MQS:
		Condition: global-multiqueue
		Prerequisites: global.multiqueuescaled

^ProductionOptimizer1:
	ProvidesPrerequisite@ProductionOptimizer1:
		Prerequisite: optimized.production1
	FreeActor@ProductionOptimizer1:
		Actor: optimized.production1
	SpawnActorOnCapture@ProductionOptimizer1:
		Actor: optimized.production1
		Delay: 1

^ProductionOptimizer2:
	ProvidesPrerequisite@ProductionOptimizer2:
		Prerequisite: optimized.production2
	FreeActor@ProductionOptimizer2:
		Actor: optimized.production2
	SpawnActorOnCapture@ProductionOptimizer2:
		Actor: optimized.production2
		Delay: 1

^ProducesBuildings:
	Inherits@QUEUEUPDATER: ^QueueUpdater
	Inherits@PRIMARY: ^PrimaryBuilding
	ProductionQueue@MQBLD:
		Type: BuildingMQ
		DisplayOrder: 1
		Group: Building
		LowPowerModifier: 250
		ReadyAudio: ConstructionComplete
		ReadyTextNotification: Construction complete.
		BlockedAudio: NoBuild
		BlockedTextNotification: Unable to build more.
		LimitedAudio: BuildingInProgress
		LimitedTextNotification: Unable to comply. Building in progress.
		QueuedAudio: Building
		OnHoldAudio: OnHold
		CancelledAudio: Cancelled
	ProductionQueue@MQDEF:
		Type: DefenseMQ
		DisplayOrder: 2
		Group: Defense
		LowPowerModifier: 250
		ReadyAudio: ConstructionComplete
		ReadyTextNotification: Construction complete.
		BlockedAudio: NoBuild
		BlockedTextNotification: Unable to build more.
		LimitedAudio: BuildingInProgress
		LimitedTextNotification: Unable to comply. Building in progress.
		QueuedAudio: Building
		OnHoldAudio: OnHold
		CancelledAudio: Cancelled
	Production@SQBLD:
		Produces: BuildingSQ, DefenseSQ
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: !global-multiqueuefull
	Production@MQBLD:
		Produces: BuildingMQ, DefenseMQ
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: global-multiqueuefull
	ProductionBar@SQBLD:
		ProductionType: BuildingSQ
		RequiresCondition: !global-multiqueuefull
	ProductionBar@SQDEF:
		ProductionType: DefenseSQ
		Color: 8A8A8A
		RequiresCondition: !global-multiqueuefull
	ProductionBar@MQBLD:
		ProductionType: BuildingMQ
		RequiresCondition: global-multiqueuefull
	ProductionBar@MQDEF:
		ProductionType: DefenseMQ
		Color: 8A8A8A
		RequiresCondition: global-multiqueuefull
	GrantConditionOnPrerequisite@MQF2:
		Condition: global-multiqueuefull
		Prerequisites: global.multiqueuefull

^ProducesInfantry:
	Inherits@QUEUEUPDATER: ^QueueUpdater
	Inherits@PRIMARY: ^PrimaryBuilding
	ProductionQueue@MQINF:
		Type: InfantryMQ
		DisplayOrder: 3
		Group: Infantry
		LowPowerModifier: 250
		ReadyAudio: UnitReady
		BlockedAudio: NoBuild
		BlockedTextNotification: Unable to build more.
		LimitedAudio: BuildingInProgress
		LimitedTextNotification: Unable to comply. Building in progress.
		QueuedAudio: Training
		OnHoldAudio: OnHold
		CancelledAudio: Cancelled
	Production@SQINF:
		Produces: InfantrySQ, Cyborg, Soldier, ParadropInfantry
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: !global-multiqueue
	Production@MQINF:
		Produces: InfantryMQ, CyborgMQ, Cyborg, Soldier, ParadropInfantry
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: global-multiqueue
	ProductionBar@SQINF:
		ProductionType: InfantrySQ
		RequiresCondition: !global-multiqueue
	ProductionBar@MQINF:
		ProductionType: InfantryMQ
		RequiresCondition: global-multiqueue
	GrantExternalConditionToProduced:
		Condition: produced
	ProductionCostMultiplier@MQS1:
		Multiplier: 300
		Prerequisites: infantry.any, global.multiqueuescaled, !optimized.production1, !optimized.production2
	ProductionCostMultiplier@MQS2:
		Multiplier: 200
		Prerequisites: infantry.any, global.multiqueuescaled, optimized.production1, !optimized.production2
	LinkedProducerSource:
	UpdatesUnitsProduced:

^ProducesCyborgs:
	Inherits: ^ProducesInfantry
	ProductionQueue@MQINF:
		Type: CyborgMQ
	Production@SQINF:
		Produces: InfantrySQ, Cyborg, ParadropInfantry, Upgrade
	Production@MQINF:
		Produces: CyborgMQ, Cyborg, Upgrade
	ProductionBar@SQINF:
		ProductionType: InfantrySQ
	ProductionBar@MQINF:
		ProductionType: CyborgMQ
	-ProductionCostMultiplier@MQS1:
	-ProductionCostMultiplier@MQS2:

^ProducesDogs:
	Inherits: ^ProducesInfantry
	ProductionQueue@MQINF:
		Type: DogMQ
	Production@SQINF:
		Produces: InfantrySQ, Dog
	Production@MQINF:
		Produces: DogMQ, Dog
	ProductionBar@SQINF:
		ProductionType: InfantrySQ
	ProductionBar@MQINF:
		ProductionType: DogMQ
	-ProductionCostMultiplier@MQS1:
	-ProductionCostMultiplier@MQS2:

^ProducesVehicles:
	Inherits@QUEUEUPDATER: ^QueueUpdater
	Inherits@PRIMARY: ^PrimaryBuilding
	ProductionQueue@MQVEH:
		Type: VehicleMQ
		DisplayOrder: 4
		Group: Vehicle
		LowPowerModifier: 250
		ReadyAudio: UnitReady
		BlockedAudio: NoBuild
		BlockedTextNotification: Unable to build more.
		LimitedAudio: BuildingInProgress
		LimitedTextNotification: Unable to comply. Building in progress.
		QueuedAudio: Building
		OnHoldAudio: OnHold
		CancelledAudio: Cancelled
	Production@SQVEH:
		Produces: VehicleSQ, ParadropVehicle
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: !global-multiqueue
	Production@MQVEH:
		Produces: VehicleMQ, ParadropVehicle
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: global-multiqueue
	ProductionBar@SQVEH:
		ProductionType: VehicleSQ
		RequiresCondition: !global-multiqueue
	ProductionBar@MQVEH:
		ProductionType: VehicleMQ
		RequiresCondition: global-multiqueue
	ProductionCostMultiplier@MQS1:
		Multiplier: 170
		Prerequisites: vehicles.any, global.multiqueuescaled, !optimized.production1, !optimized.production2
	ProductionCostMultiplier@MQS2:
		Multiplier: 135
		Prerequisites: vehicles.any, global.multiqueuescaled, optimized.production1, !optimized.production2
	GrantExternalConditionToProduced:
		Condition: produced
	LinkedProducerSource:
	UpdatesUnitsProduced:

^ProducesNaval:
	Inherits@QUEUEUPDATER: ^QueueUpdater
	Inherits@PRIMARY: ^PrimaryBuilding
	ProductionQueue@MQNAV:
		Type: ShipMQ
		DisplayOrder: 5
		Group: Ship
		LowPowerModifier: 250
		ReadyAudio: UnitReady
		BlockedAudio: NoBuild
		BlockedTextNotification: Unable to build more.
		LimitedAudio: BuildingInProgress
		LimitedTextNotification: Unable to comply. Building in progress.
		QueuedAudio: Building
		OnHoldAudio: OnHold
		CancelledAudio: Cancelled
	Production@SQNAV:
		Produces: ShipSQ, Boat, Submarine
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: !global-multiqueue
	Production@MQNAV:
		Produces: ShipMQ, Boat, Submarine
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: global-multiqueue
	ProductionBar@SQNAV:
		ProductionType: ShipSQ
		RequiresCondition: !global-multiqueue
	ProductionBar@MQNAV:
		ProductionType: ShipMQ
		RequiresCondition: global-multiqueue
	UpdatesUnitsProduced:

^ProducesUpgrades:
	Inherits@QUEUEUPDATER: ^QueueUpdater
	Production@UPG:
		Produces: Upgrade
		PauseOnCondition: forceshield || being-warped
	ProductionBar@UPG:
		ProductionType: Upgrade
	Exit@UPG:
		ProductionTypes: Upgrade
	ProvidesPrerequisite@UpgradesProducer:
		Prerequisite: upgrades.producer
		RequiresCondition: !(forceshield || being-warped)
	-GrantConditionOnPrerequisite@MQF:
	-GrantConditionOnPrerequisite@MQS:

^ProducesAircraft:
	Inherits@QUEUEUPDATER: ^QueueUpdater
	Inherits@PRIMARY: ^PrimaryBuilding
	ProductionQueue@MQAIR:
		Type: AircraftMQ
		DisplayOrder: 6
		Group: Aircraft
		LowPowerModifier: 250
		ReadyAudio: UnitReady
		BlockedAudio: NoBuild
		BlockedTextNotification: Unable to build more.
		LimitedAudio: BuildingInProgress
		LimitedTextNotification: Unable to comply. Building in progress.
		QueuedAudio: Building
		OnHoldAudio: OnHold
		CancelledAudio: Cancelled
	ProvidesPrerequisite@any:
		Prerequisite: aircraft.any
	Production@SQAIR:
		Produces: AircraftSQ, Plane, Helicopter
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: !global-multiqueue
	Production@MQAIR:
		Produces: AircraftMQ, Plane, Helicopter
		PauseOnCondition: forceshield || being-warped
		RequiresCondition: global-multiqueue
	ProductionBar@SQAIR:
		ProductionType: AircraftSQ
		RequiresCondition: !global-multiqueue
	ProductionBar@MQAIR:
		ProductionType: AircraftMQ
		RequiresCondition: global-multiqueue
	ProductionCostMultiplier@MQS1:
		Multiplier: 160
		Prerequisites: aircraft.any, global.multiqueuescaled, !optimized.production1, !optimized.production2
	ProductionCostMultiplier@MQS2:
		Multiplier: 130
		Prerequisites: aircraft.any, global.multiqueuescaled, optimized.production1, !optimized.production2
	GrantExternalConditionToProduced:
		Condition: produced
	UpdatesUnitsProduced:

^ProducesHelicopters:
	Inherits@AIR: ^ProducesAircraft
	Production@SQAIR:
		Produces: AircraftSQ, Helicopter
	Production@MQAIR:
		Produces: AircraftMQ, Helicopter
