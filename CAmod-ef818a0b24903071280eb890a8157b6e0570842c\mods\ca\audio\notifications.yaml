Speech:
	DefaultVariant: .aud
	Prefixes:
		allies: r_
		england: r_
		france: r_
		germany: r_
		usa: r_
		soviet: r2_
		russia: r2_
		ukraine: r2_
		iraq: r2_
		yuri: r2_
		gdi: c_
		talon: c2_
		zocom: c_
		eagle: c_
		arc: c_
		nod: n_
		blackh: n2_
		marked: n_
		legion: n_
		shadow: n_
		scrin: s_
		reaper: s_
		traveler: s_
		harbinger: s_
		collector: s_
	Notifications:
		AbombAvailable: aavail1
		AbombLaunchDetected: alaunch1
		AbombPrepping: aprep1
		AbombReady: aready1
		AirUnitLost: aunitl1
		AlliedForcesApproaching: aappro1
		AlliedForcesFallen: afallen1
		AlliedForcesSelected: aselect1
		AlliedReinforcementsArrived: aarrive1
		AlliedReinforcementsEast: aarive1
		AlliedReinforcementsNorth: aarrivn1
		AlliedReinforcementsSouth: aarrivs1
		AlliedReinforcementsWest: aarrivw1
		BaseAttack: baseatk1
		Building: abldgin1
		BuildingCannotPlaceAudio: nodeply1
		BuildingCaptured: strucap1
		BuildingInfiltrated: bldginf1
		BuildingInProgress: progres1
		BuildingProgress: bldgprg1
		Cancelled: cancld1
		ChronosphereCharging: chrochr1
		ChronosphereReady: chrordy1
		ChronosphereTestSuccessful: chroyes1
		CommandCenterAttack: cmdcntr1
		CommandoFreed: comndof1
		CommandoRescued: comndor1
		ConstructionComplete: conscmp1
		ControlCenterDeactivated: cntlded1
		ConvoyApproaching: convyap1
		ConvoyUnitLost: convlst1
		CreditsStolen: credit1
		CivilianBuildingCaptured: strucap1
		EnemyUnitsApproaching: enmyapp1
		EnemyPlanesApproaching: enmyapp1
		EnemyDetected: enmydet
		EnemyUnitStolen: enmyunitsto1
		ExplosiveChargePlaced: xploplc1
		FirstObjectiveMet: 1objmet1
		FourtyMinutesRemaining: 40minr
		GameLoaded: load1
		GameSaved: save1
		HarvesterAttack: harvatk1
		InsufficientFunds: nofunds1
		InsufficientPower: nopowr1
		IronCurtainCharging: ironchg1
		IronCurtainReady: ironrdy1
		KosyginFreed: kosyfre1
		KosyginRescued: kosyres1
		Leave: bct1
		Lose: misnlst1
		LowPower: lopower1
		MercenaryFreed: mercf1
		MercenaryRescued: mercr1
		MissionAccomplished: misnwon1
		MissionFailed: misnlst1
		MissionLoaded: load1
		MissionSaved: save1
		MissionTimerInitialised: mtimein1
		MothershipDeployed: mshpd1
		NavalUnitLost: navylst1
		NewOptions: newopt1
		NoBuild: nobuild1
		ObjectiveMet: objmet1
		ObjectiveNotMet: objnmet1
		ObjectiveNotReached: objnrch1
		ObjectiveReached: objrch1
		OnHold: onhold1
		OperationControlTerminated: opterm1
		OurBuildingCaptured: bldcap1
		OurTechnologyLocked: ourttechlckd1
		OurTechnologyStolen: ourtechsto1
		PrimaryBuildingSelected: pribldg1
		Promoted: promtd1
		ReinforcementsArrived: reinfor1
		Reinforce: reinforava
		Repairing: repair1
		SatelliteLaunched: satlnch1
		SecondObjectiveMet: 2objmet1
		SelectTarget: slcttgt1
		SignalFlare: flare1
		SignalFlareEast: flaree1
		SignalFlareNorth: flaren1
		SignalFlareSouth: flares1
		SignalFlareWest: flarew1
		SilosNeeded: silond1
		SonarPulseReady: pulse1
		SovietEmpireFallen: sovefal1
		SovietEmpireSelected: sovemp1
		SovietForcesApproaching: sovfapp1
		SovietForcesFallen: sovforc1
		SovietReinforcementsArrived: sovrein1
		SpyPlaneReady: spypln1
		ReconDroneReady: rdronerdy1
		StartGame: bctrinit
		StructureDestroyed: strckil1
		StructureSold: strusld1
		StructureLost: strclst1
		TanyaFreed: tanyaf1
		TanyaRescued: tanyar1
		TargetFreed: targfre1
		TargetRescued: targres1
		TechnologyAcquired: techacq1
		TechnologyLocked: techlckd1
		TenMinutesRemaining: 10minr
		ThirdObjectiveMet: 3objmet1
		ThirtyMinutesRemaining: 30minr
		TimerStarted: timergo1
		TimerStopped: timerno1
		Training: train1
		TwentyMinutesRemaining: 20minr
		UnitArmorUpgraded: armorup1
		UnitFirepowerUpgraded: firepo1
		UnitUpgraded: unitupg1
		UnitFull: unitful1
		UnitLost: unitlst1
		UnitReady: unitrdy1
		UnitRepaired: unitrep1
		UnitSold: unitsld1
		UnitSpeedUpgraded: unitspd1
		UnitStolen: unitsto
		UpgradeComplete: upgrade
		WarningOneMinuteRemaining: 1minr
		WarningTwoMinutesRemaining: 2minr
		WarningThreeMinutesRemaining: 3minr
		WarningFourMinutesRemaining: 4minr
		WarningFiveMinutesRemaining: 5minr
		Win: misnwon1

		AirstrikeReady: astriker1
		MissileLaunchDetected: missld1
		ForceShieldCharging: forcechg1
		ForceShieldReady: forcedr1

		InfluenceLevel1: influencelevel1
		InfluenceLevel2: influencelevel2
		VeilOfWarReady: vowrdy1
		ClusterMinesReady: cminesrdy1
		TemporalIncursionReady: tempincrdy1
		StrafingRunReady: strafrdy1
		TimeWarpReady: twrprdy1
		CoalitionSelected: coalitionsel1
		CoalitionActive: coalitionrdy1
		CryostormReady: cryostormrdy1
		CryostormWarning: cryostormapp1
		HeliosBombReady: heliosrdy1
		BlackSkyStrikeReady: bskyrdy1
		BlackSkyStrikeWarning: bskywarn1
		StormReady: stormrdy1
		StormApproach: stormapp1

		ParabombsReady: parabrdy1
		ParatroopersReady: paratrdy1
		StormtroopersReady: stmtrprdy1
		AtomicBombReady: atmbombrdy1
		AtomicShellsReady: atmshelrdy1
		CarpetBombReady: carpetrdy1
		ChaosBombsReady: chaosbombsrdy1
		GeneticMutationBombReady: gmutrdy1
		DoctrineSelected: doctrinesel1
		HeroesOfTheUnionReady: heroesrdy1
		TankDropReady: tankdroprdy1
		KillZoneReady: killzonerdy1

		DropPodsAvailable: dpodrdy1
		NaniteShieldReady: nanrdy1
		NaniteShieldCharging: nanchg1
		InterceptorsReady: intrdy1
		NaniteRepairReady: nreprdy1
		XODropAvailable: xoavail1
		SuppliesInbound: suppinb
		FireStormReady: fsready
		FireStormOffline: fsoffline
		Bombardment: bombardment
		HoldTheLine: holdtheline
		SeekAndDestroy: seekanddestroy
		SurgicalStrikeReady: surgicalrdy1
		EMMissileReady: emmisrdy1
		EMMissilePrepping: emmprep1
		IonCannonCharging: ionchrg1
		IonCannonReady: ionredy1
		IonCannonApproach: ioncanapp

		CovenantsAvailable: covavail1
		SatelliteHackReady: sathackrdy1
		InfernoBombReady: infernordy1
		CashHackReady: cashhkrdy1
		TechHackReady: techhkrdy1
		AssassinSquadReady: assardy1
		HackerCellReady: hackrdy1
		ConfessorCabalReady: confcabrdy1
		ShadowTeamReady: shadtmrdy1
		FrenzyReady: frenzyrdy1
		ClusMissileReady: clusrdy
		ClusMissileWarning: cluswarn1
		ChemStealthCharging: chemstch1
		ChemStealthReady: chemstrdy1
		ChemMissileReady: cmissrdy1
		ChemMissileWarning: cmisswarn1

		VoidSpikeReady: voidspkrdy1
		IchorSeedReady: ichsdrdy
		ResourceScanReady: rscanrdy
		StormSpikeReady: strmspkrdy
		IonSurgeReady: ionsrgrdy
		BuzzerSwarmReady: buzzrdy
		GreaterCoalescenceReady: grcoalrdy
		AllegianceDeclarationAvailable: aldecavail1
		OverlordsWrathReady: owrathrdy1
		OverlordsWrathWarning: owrathwarn1
		GatewayReady: gwayrdy1
		AnathemaReady: anathrdy1
		FleetRecallReady: fleetrecallrdy1
		SuppressionCharging: sprschg
		SuppressionReady: sprsrdy
		RiftReady: riftrdy
		RiftWarning: riftwarn1
		SubjugationReady: subjrdy1

Sounds:
	Notifications:
		RadarUp: radaron2
		RadarDown: radardn1
		CashTickUp: tone15
			VolumeModifier: 0.33
			InterruptType: Overlap
		CashTickDown: tone16
			VolumeModifier: 0.33
			InterruptType: Overlap
		LevelUp: lvlup1
			VolumeModifier: 0.33
		DisablePower: powrdn1
		EnablePower: gpowerup
		ChatLine: boop
			InterruptType: Interrupt
		ClickSound: click
			InterruptType: Interrupt
		ClickDisabledSound:
		Beacon: beacon
			InterruptType: Interrupt
		AlertBuzzer: buzzy1
		AlertBleep: bleep6
		BaseSetup: bleep9
		PlayerRankUp: bleep6

		UPG-railgun: vtitupg1
		UPG-ionmam: vmamupg1
		UPG-mdrone: vmamdupg1
		UPG-bdrone: vbdrnupg1
		UPG-hovermam: vmamupg2
		UPG-microwave: vmwtupg1
		UPG-seismic: vsukupg1
		UPG-tibcore: vtibcupg1
