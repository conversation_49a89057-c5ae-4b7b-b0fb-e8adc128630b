﻿#region Copyright & License Information
/**
 * Copyright (c) The OpenRA Combined Arms Developers (see CREDITS).
 * This file is part of OpenRA Combined Arms, which is free software.
 * It is made available to you under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of the License,
 * or (at your option) any later version. For more information, see COPYING.
 */
#endregion

using OpenRA.GameRules;
using OpenRA.Mods.Common.Effects;
using OpenRA.Traits;

namespace OpenRA.Mods.CA.Warheads
{
	public class RevealShroudWarhead : WarheadAS
	{
		[Desc("PlayerRelationships relative to the firer which the warhead affects.")]
		public readonly PlayerRelationship RevealStances = PlayerRelationship.Ally;

		[Desc("Duration of the reveal.")]
		public readonly int Duration = 25;

		[Desc("Radius of the reveal around the detonation.")]
		public readonly WDist Radius = new WDist(1536);

		[Desc("Can this warhead reveal shroud generated by the GeneratesShroud trait?")]
		public readonly bool RevealGeneratedShroud = false;

		public override void DoImpact(in Target target, WarheadArgs args)
		{
			var firedBy = args.SourceActor;
			if (!target.IsValidFor(firedBy))
				return;

			if (!IsValidImpact(target.CenterPosition, firedBy))
				return;

			// Lambdas can't use 'in' variables, so capture a copy for later
			var centerPosition = target.CenterPosition;

			if (!firedBy.IsDead)
			{
				firedBy.World.AddFrameEndTask(w => w.Add(new RevealShroudEffect(centerPosition, Radius,
					RevealGeneratedShroud ? Shroud.SourceType.Visibility : Shroud.SourceType.PassiveVisibility,
					firedBy.Owner, RevealStances, duration: Duration)));
			}
		}
	}
}
