^Palettes:
	TintPostProcessEffect:
		Red: 0.80
		Green: 0.85
		Blue: 1.00
		Ambient: 0.9

World:
	MissionData:
		Briefing: ================\n\n- Each player starts with a single Commando.\n-------------------------\n- If your Commando dies you can build a new one (takes longer for each death).\n-------------------------\n- Fight your way through the Scrin forces.\n-------------------------\n- Objective is to find and destroy the Scrin portals.\n-------------------------\n- Mission failed if all spawn points are lost and all Commandos are dead.\n\n================\n
	LuaScript:
		Scripts: scrinfestation.lua
	StartingUnits@rmboonly:
		Class: none
		ClassName: Commando Only
		BaseActor: rmbo
		Factions: allies, england, france, germany, usa, soviet, russia, ukraine, iraq, yuri, gdi, talon, zocom, eagle, arc, nod, blackh, marked, legion, shadow, scrin, reaper, traveler, harbinger, collector
	-StartingUnits@mcvonly:
	-StartingUnits@lightallies:
	-StartingUnits@lightsoviet:
	-StartingUnits@heavyallies:
	-StartingUnits@heavysoviet:
	-StartingUnits@mcvonly2:
	-StartingUnits@defaultgdia:
	-StartingUnits@defaultnoda:
	-StartingUnits@heavynoda:
	-StartingUnits@heavygdia:
	-StartingUnits@mcvonlyscrin:
	-StartingUnits@lightscrin:
	-StartingUnits@heavyscrin:
	SpawnStartingUnits:
		DropdownVisible: False
	MapBuildRadius:
		AllyBuildRadiusCheckboxVisible: False
		BuildRadiusCheckboxVisible: False
	MapOptions:
		TechLevelDropdownVisible: False
		ShortGameCheckboxEnabled: True
		ShortGameCheckboxLocked: True
		ShortGameCheckboxVisible: False
	CrateSpawner:
		CheckboxEnabled: False
		CheckboxLocked: True
		CheckboxVisible: False
	MapStartingLocations:
		SeparateTeamSpawnsCheckboxVisible: False
	TimeLimitManager:
		TimeLimitLocked: True
		TimeLimitDropdownVisible: False

Player:
	PlayerResources:
		SelectableCash: 0
		DefaultCash: 0
		DefaultCashDropdownVisible: False
	LobbyPrerequisiteCheckbox@GLOBALBOUNTY:
		Enabled: False
		Locked: True
		Visible: False
	LobbyPrerequisiteCheckbox@FORCESHIELD:
		Enabled: False
		Visible: False
	DeveloperMode:
		CheckboxLocked: True
		CheckboxVisible: False
	LobbyPrerequisiteCheckbox@GLOBALFACTUNDEPLOY:
		Visible: False
	LobbyPrerequisiteCheckbox@NAVY:
		Visible: False
	LobbyPrerequisiteCheckbox@BALANCEDHARVESTING:
		Visible: False
	LobbyPrerequisiteCheckbox@FASTREGROWTH:
		Visible: False
	LobbyPrerequisiteCheckbox@REVEALONFIRE:
		Enabled: False
		Locked: True
		Visible: False
	LobbyPrerequisiteDropdown@QUEUETYPE:
		Default: global.singlequeue
		Visible: False
		Values:
			global.singlequeue: options-queuetype.singlequeue
	Shroud:
		ExploredMapCheckboxEnabled: False
		ExploredMapCheckboxVisible: False
		ExploredMapCheckboxLocked: True
	ModularBot@DormantAI:
		Name: Dormant AI
		Type: dormant
	-ModularBot@BrutalAI:
	-ModularBot@VeryHardAI:
	-ModularBot@HardAI:
	-ModularBot@NormalAI:
	-ModularBot@EasyAI:
	-ModularBot@NavalAI:

RMBO:
	Valued:
		Cost: 0
	Buildable:
		Prerequisites: ~rmbospawn
		BuildDuration: 500
		BuildDurationModifier: 100
	TooltipExtras:
		Strengths: • Strong vs Infantry
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Maximum 1 can be trained\n• Can detect cloaked units\n• Immune to mind control\n• Voiced by Frank Klepacki
	-Armament@sapper:
	-GrantConditionOnPrerequisite@HAZMATZOCOM:
	SpawnActorOnDeath@ONEDEATH:
		Actor: death.first
		RequiresCondition: !one-death
	SpawnActorOnDeath@TWODEATHS:
		Actor: death.second
		RequiresCondition: one-death && !two-deaths
	SpawnActorOnDeath@THREEDEATHS:
		Actor: death.third
		RequiresCondition: two-deaths && !three-deaths
	SpawnActorOnDeath@FOURDEATHS:
		Actor: death.fourth
		RequiresCondition: three-deaths && !four-deaths
	SpawnActorOnDeath@FIVEDEATHS:
		Actor: death.fifth
		RequiresCondition: five-deaths && !five-deaths
	GrantConditionOnPrerequisite@ONEDEATH:
		Condition: one-death
		Prerequisites: death.first
	GrantConditionOnPrerequisite@TWODEATHS:
		Condition: two-deaths
		Prerequisites: death.second
	GrantConditionOnPrerequisite@THREEDEATHS:
		Condition: three-deaths
		Prerequisites: death.third
	GrantConditionOnPrerequisite@FOURDEATHS:
		Condition: four-deaths
		Prerequisites: death.fourth
	GrantConditionOnPrerequisite@FIVEDEATHS:
		Condition: five-deaths
		Prerequisites: death.fifth
	ProductionTimeMultiplier@TWODEATHS:
		Multiplier: 150
		Prerequisites: death.second, !death.third
	ProductionTimeMultiplier@THREEDEATHS:
		Multiplier: 300
		Prerequisites: death.third, !death.fourth
	ProductionTimeMultiplier@FOURDEATHS:
		Multiplier: 450
		Prerequisites: death.fourth, !death.fifth
	ProductionTimeMultiplier@FIVEDEATHS:
		Multiplier: 600
		Prerequisites: death.fifth
	WithNameTagDecorationCA:
		Position: Top
		Font: Regular
		Margin: 0, -20
		ColorSource: Player
		ContrastColorLight: 000000

RMBOSPAWN:
	Inherits: CAMERA
	Inherits@PROD: ^ProducesInfantry
	Tooltip:
		Name: Spawn Point
	MustBeDestroyed:
		RequiredForShortGame: true
	Exit:
	Health:
		HP: 5000
	Armor:
		Type: Wood
	HitShape:
	ProvidesPrerequisite:
	Targetable:
		TargetTypes: Ground, Structure
	WithSpriteBody:
	-RenderSpritesEditorOnly:
	RenderSprites:
		Image: shab
	WithIdleAnimation:
		Sequences: idle
		Interval: 25
	FireWarheadsOnDeath:
		Weapon: UnitExplodeSmall
		EmptyWeapon: UnitExplodeSmall
	Production@SQINF:
		-PauseOnCondition:
	Production@MQINF:
		-PauseOnCondition:
	ScriptTriggers:

WORMHOLE:
	MapEditorData:
		Categories: System
	MustBeDestroyed:
		RequiredForShortGame: true
	Health:
		HP: 50000

^DeathToken:
	AlwaysVisible:
	Interactable:
	ScriptTriggers:
	ProvidesPrerequisite@DEATH:

DEATH.FIRST:
	Inherits: ^DeathToken

DEATH.SECOND:
	Inherits: ^DeathToken

DEATH.THIRD:
	Inherits: ^DeathToken

DEATH.FOURTH:
	Inherits: ^DeathToken

DEATH.FIFTH:
	Inherits: ^DeathToken

MEDI:
	Mobile:
		PauseOnCondition: immobile
	GrantCondition@IMMOBILE:
		Condition: immobile
