^Palettes:
	PaletteFromFile@terrain-temperat:
		Filename: alien.pal
	TintPostProcessEffect:
		Red: 1.3
		Green: 1
		Blue: 1.5
		Ambient: 0.5

^BaseWorld:
	TerrainLighting:

World:
	LuaScript:
		Scripts: campaign.lua, foothold.lua
	MissionData:
		Briefing: ———[    Chapter VI Introduction    ]———\n\nAs the Scrin mothership fell from the sky in flames, the cyborgs surrounding it revealed their true allegiance. The Scrin, stranded in both space and time, were manipulated into opening a gateway to their homeworld with <PERSON>'s army at the threshold. \n\nWhile his ultimate objective is unclear, GDI are unwilling to let him pursue it uncontested. The gateway has remained open, but is rapidly destabilizing and it appears likely that it will collapse soon. Time is running out for GDI to give chase.\n\n———[    Mission Briefing (GDI)   ]———\n\nOur forces are almost ready and our recon drones have confirmed that the atmosphere and climatic conditions on the other side of the gateway will support human life.\n\nClearly, this is uncharted territory. We must establish a foothold from which we can base our operations. We have detected minimal Scrin presence in the immediate vicinity of our destination, and no sign of <PERSON> or his army.\n\nShortly after <PERSON> departed we detected an energy surge emanating from the Scrin signal transmitter. We believe this was caused by the gateway's exit point being shifted. <PERSON> clearly didn't want us following closely behind, but it does beg the question; why did he leave the door open at all?\n\nThe gateway has become too unstable for heavier equipment to make it through, but research into Scrin wormhole technology gathered since their arrival has provided us with some understanding of how the portals work.\n\nA gateway requires a bi-directional flow of energy to be sustained. Our side is taken care of, but without a corresponding transmission from the other side the gateway will eventually lose cohesion.\n\nScrin Nerve Centers are capable of providing such an energy flow, so if we can locate and capture one close enough to the exit point, the gateway should be stabilized.\n\nYour first objective is to deploy sensor arrays in the area around the exit. We should then be able to detect a suitable Nerve Center to capture, allowing us to bring heavier forces and establish a base. Then the area must be cleared of any remaining hostile forces.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: whatlurk

Player:
	PlayerResources:
		DefaultCash: 6000

FLARE.sensor:
	Inherits: FLARE
	Tooltip:
		Name: Sensor Deployment Zone
	WithRangeCircle:
		Type: SensorZone
		Visible: Always
		Color: ead37740
		Range: 10c0
	RevealsShroud:
		Range: 1c512

deployedsensortoken:
	Inherits: ^InvisibleDummy
	Mobile:
		Locomotor: cloud
		Speed: 1
	KillsSelf:
		Delay: 26

MSAR:
	LaysMinefield@token:
		Mines: deployedsensortoken
		MineSelectionMode: Shuffled
		Locations: 0,0
		RecreationInterval: 25
		RequiresCondition: deployed

NERV:
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	CaptureManager:
		-BeingCapturedCondition:
	Power:
		Amount: 0
	-DetonateWeaponPower@STORMSPIKE:

SILO.SCRIN:
	Power:
		Amount: 0

WORMHOLE:
	-Targetable:
	-RallyPoint:
	-Selectable:
	Interactable:
	Tooltip:
		Name: Unstable Wormhole

WORMHOLESTABLE:
	Inherits: WORMHOLE
	Tooltip:
		Name: Stabilized Wormhole
	TerrainLightSource:
		Range: 3c0
		Intensity: 0.5
		BlueTint: 1
		RedTint: 0.2

# Enable subfaction specific tech

XO:
	Buildable:
		Prerequisites: gtek, ~vehicles.gdi

WOLV:
	Buildable:
		Prerequisites: gtek, ~vehicles.gdi

PBUL:
	Buildable:
		Prerequisites: gtek, ~vehicles.gdi

STWR:
	Buildable:
		Prerequisites: vehicles.any, ~structures.gdi

ionmam.upgrade:
	Buildable:
		Prerequisites: upgc, !mdrone.upgrade, !hovermam.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

hovermam.upgrade:
	Buildable:
		Prerequisites: upgc, !ionmam.upgrade, !mdrone.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

mdrone.upgrade:
	Buildable:
		Prerequisites: upgc, !ionmam.upgrade, !hovermam.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

# Disable tech

HQ:
	-DropPodsPowerCA@Zocom:
	-AirstrikePowerCA@uav:
	-AirstrikePowerCA@uavST:

AFLD.GDI:
	-InterceptorPower@AirDef:

EYE:
	Inherits@CAMPAIGNDISABLED: ^Disabled

UPGC.DROP:
	Inherits@CAMPAIGNDISABLED: ^Disabled

# Hunt() requires only 1 AttackBase
DEVA:
	-AttackFrontalCharged:
	-Armament@PRIMARYUPG:
	-AmbientSound@CHARGE:
	-WithIdleOverlay@CHARGE1:
	-WithIdleOverlay@CHARGE2:
	-WithIdleOverlay@CHARGE3:
