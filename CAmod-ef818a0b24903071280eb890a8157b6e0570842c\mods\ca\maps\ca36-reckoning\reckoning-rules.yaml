^Palettes:
	PaletteFromFile@terrain-temperat:
		Filename: alien.pal
	TintPostProcessEffect:
		Red: 1.3
		Green: 1
		Blue: 1.5
		Ambient: 0.5
	FixedColorPalette@BlueTiberium:
		Color: ffffff

^BaseWorld:
	TerrainLighting:
	ResourceRenderer:
		ResourceTypes:
			BlueTiberium:
				Name: Purified Tiberium

^BasePlayer:
	PlayerResources:
		ResourceValues:
			BlueTiberium: 25

World:
	LuaScript:
		Scripts: campaign.lua, reckoning.lua
	MissionData:
		Briefing: The device worked perfectly. Scrin for many miles around are no longer dependent on Tiberium to survive, or have had their dependence significantly reduced. With additional uses of the device, all Scrin can be free of their affliction.\n\nFor us, it means that the Earth can be similarly cleansed, and the tremendous power of Tiberium can be leveraged without side-effects.\n\nNews spread quickly amongst the Scrin of the Overlord's deception. Of course, those willing to trust the word of a human remain a minority, and the effects of the device have not yet spread widely enough to aid in persuading the wider Scrin population.\n\nMany Scrin have risen up against the Overlord's armies however, and we are in the midst of a civil war.\n\nThe Overlord's forces are mobilizing across the planet, so we have precious little time to strike a decisive blow. I have been contacted by a leader amongst the rebels who has indicated where the <PERSON>lord's most elite forces are amassing, and it has been suggested that defeating him there will weaken his authority enough to tip the scales in the rebels' favor. At least until the device has made the enormity of what is transpiring evident to all Scrin.\n\nThe rebels have engaged these forces already. Our assistance will be unexpected thanks to our brothers fighting on multiple other fronts, so I want you to take a small force, aid the rebels while our forces accumulate, then destroy the Overlord's primary base in the area before his armies from across the planet arrive.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: gateway

Player:
	PlayerResources:
		DefaultCash: 6000

# Purified Tib doesn't damage infantry

^Infantry:
	DamagedByTerrain@TIBDAMAGE:
		Terrain: Tiberium

TMPL:
	GrantExternalConditionPowerCA@Frenzy:
		-Prerequisites:

ACOL:
	Buildable:
		Prerequisites: obliortmpl, anyradar, ~infantry.nod, ~!tmpp

TPLR:
	Buildable:
		Prerequisites: obliortmpl, anyradar, ~infantry.nod, ~tmpp

BH:
	Buildable:
		Prerequisites: anyradar, ~infantry.nod

LTNK:
	Buildable:
		Prerequisites: ~vehicles.nod, ~!lastnk.upgrade

LTNK.Laser:
	Buildable:
		Prerequisites: ~vehicles.nod, ~lastnk.upgrade

MTNK:
	Buildable:
		Prerequisites: ~!bdrone.upgrade, ~vehicles.gdi

MTNK.Laser:
	Inherits@CAMPAIGNDISABLED: ^Disabled

FTNK:
	Buildable:
		Prerequisites: anyradar, ~vehicles.nod

HFTK:
	Buildable:
		Prerequisites: anyradar, ~vehicles.nod

WTNK:
	Buildable:
		Prerequisites: anyradar, ~vehicles.nod

MLRS:
	Buildable:
		Prerequisites: tmpl, ~vehicles.nod

SPEC:
	Buildable:
		Prerequisites: tmpl, ~vehicles.nod

VENM:
	Buildable:
		Prerequisites: ~aircraft.nod

APC2:
	Buildable:
		Prerequisites: ~vehicles.gdi, ~!vulcan.upgrade

AIRS:
	Inherits@CAMPAIGNDISABLED: ^Disabled

WEAP.TD:
	Buildable:
		Prerequisites: anyrefinery, ~structures.td

blacknapalm.upgrade:
	Buildable:
		Prerequisites: ~player.nod, tmpl

quantum.upgrade:
	Buildable:
		Prerequisites: ~player.nod, tmpl

hstk.upgrade:
	Buildable:
		Prerequisites: ~player.nod, tmpl

microwave.upgrade:
	Buildable:
		Prerequisites: ~player.nod, tmpl

NERV:
	DetonateWeaponPower@BUZZERSWARMAI:
		Prerequisites: nerv
		ChargeInterval: 7500
	DetonateWeaponPower@STORMSPIKE:
		Prerequisites: nerv
		ChargeInterval: 8250

SCRT:
	MeteorPower@OverlordsWrath:
		Prerequisites: botplayer

SPLITBLUE:
	Tooltip:
		Name: Blossom Tree

WORMHOLE:
	-Targetable:
	RenderSprites:
		Image: wormholelg

# Hunt() requires only 1 AttackBase
DEVA:
	-AttackFrontalCharged:
	-Armament@PRIMARYUPG:
	-AmbientSound@CHARGE:
	-WithIdleOverlay@CHARGE1:
	-WithIdleOverlay@CHARGE2:
	-WithIdleOverlay@CHARGE3:
