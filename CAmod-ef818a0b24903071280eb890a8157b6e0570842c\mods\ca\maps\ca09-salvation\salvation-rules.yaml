^Palettes:
	TintPostProcessEffect:

World:
	LuaScript:
		Scripts: campaign.lua, salvation.lua
	MissionData:
		Briefing: The visitors, the Scrin, have taken control of a peninsula that is home to many innocent people, in order to seed it with Tiberium.\n\nThese peoples' cries for help have been predictably ignored. The Brotherhood is their only hope. Their homes, and indeed their lives, are in our hands.\n\nTake a detachment and liberate the peninsula. Show these people that after having been forsaken by all others, the Brotherhood of Nod will protect them, and in turn I am certain that many of them will join our cause.\n\nAs the Scrin control the Tiberium fields here, you will need to take control of the oil derricks here to fund your efforts.
	MapOptions:
		ShortGameCheckboxEnabled: False
		TechLevel: medium
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: chrg226m

Player:
	PlayerResources:
		DefaultCash: 0

# Enable subfaction specific tech

BH:
	Buildable:
		Prerequisites: anyradar

# Disable tech

SAPC:
	Inherits@CAMPAIGNDISABLED: ^Disabled

AMCV:
	Inherits@CAMPAIGNDISABLED: ^Disabled

MLRS:
	Inherits@CAMPAIGNDISABLED: ^Disabled

RMBO:
	Inherits@CAMPAIGNDISABLED: ^Disabled

ENLI:
	Inherits@CAMPAIGNDISABLED: ^Disabled

RMBC:
	Inherits@CAMPAIGNDISABLED: ^Disabled

# Disable powers

HQ:
	-SpawnActorPowerCA@sathack:
	-SpawnActorPowerCA@sathacklegion:

# Misc

tibcore.upgrade:
	-AnnounceOnCreation:

HOSP:
	-GrantConditionOnPrerequisite@OwnedByAi:
	-PeriodicProducerCA@MEDIC:
	-PeriodicProducerCA@REJUVENATOR:
	-GrantConditionIfOwnerIsNeutral:
	-GrantConditionOnPrerequisite@SCRIN:
	TooltipExtras:
		Description: When controlled, heals nearby infantry.

S1:
	AutoTarget:
		ScanRadius: 7

S2:
	AutoTarget:
		ScanRadius: 7

S3:
	AutoTarget:
		ScanRadius: 7

S4:
	AutoTarget:
		ScanRadius: 7

GUNW:
	AutoTarget:
		ScanRadius: 7

SHRW:
	AutoTarget:
		ScanRadius: 7

SEEK:
	AutoTarget:
		ScanRadius: 7

LACE:
	AutoTarget:
		ScanRadius: 7

CORR:
	AutoTarget:
		ScanRadius: 7

LCHR:
	AutoTarget:
		ScanRadius: 7

DEVO:
	AutoTarget:
		ScanRadius: 7

ATMZ:
	AutoTarget:
		ScanRadius: 7

INTL:
	AutoTarget:
		ScanRadius: 7

GSCR:
	AutoTarget:
		ScanRadius: 7

STCR:
	AutoTarget:
		ScanRadius: 7

TPOD:
	AutoTarget:
		ScanRadius: 7

RTPD:
	AutoTarget:
		ScanRadius: 7

WORMHOLE:
	MustBeDestroyed:
		RequiredForShortGame: true
	Health:
		HP: 100000

OILB:
	CashTrickler:
		Interval: 375
		Amount: 200

# Hunt() requires only 1 AttackBase
DEVA:
	-AttackFrontalCharged:
	-Armament@PRIMARYUPG:
	-AmbientSound@CHARGE:
	-WithIdleOverlay@CHARGE1:
	-WithIdleOverlay@CHARGE2:
	-WithIdleOverlay@CHARGE3:
