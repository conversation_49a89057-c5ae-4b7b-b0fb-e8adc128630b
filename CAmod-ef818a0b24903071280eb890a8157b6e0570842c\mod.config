##############################################################################
# Core Configuration
#
# Basic settings that should be changed by all projects.
##############################################################################

# The id of the mod packaged by this project.
# This must exist as a directory in the mods directory and should not contain spaces.
MOD_ID="ca"

# The OpenRA engine version to use for this project.
ENGINE_VERSION="ca-engine/1.08"

##############################################################################
# Packaging
#
# Settings controlling the creation of installers.
##############################################################################

# The prefix used for the installer filenames.
#  - Windows installers will be named as {PACKAGING_INSTALLER_NAME}-{TAG}.exe
#  - macOS installers will be named as {PACKAGING_INSTALLER_NAME}-{TAG}.dmg
#  - Linux .appimages will be named as {PACKAGING_INSTALLER_NAME}-${TAG}.AppImage
PACKAGING_INSTALLER_NAME="CombinedArms"

# The human-readable name for this project.
# This is used in:
#  - Crash dialogs (all platforms)
#  - macOS .app bundle name
#  - macOS menu bar
#  - macOS "About" window
#  - macOS disk image title
#  - Windows installer
#  - Windows start menu
#  - Windows desktop shortcut
#  - Windows "Programs and Features" list
#  - Linux launcher shortcut
PACKAGING_DISPLAY_NAME="Combined Arms"

# The URL for the project homepage.
# This is used in:
#  - Windows "Add/Remove Programs" list
PACKAGING_WEBSITE_URL="https://www.moddb.com/mods/command-conquer-combined-arms"

# The URL that is displayed in the crash dialog.
PACKAGING_FAQ_URL="https://wiki.openra.net/FAQ"

# The human-readable project authors.
# This is used in:
#  - Windows "Add/Remove Programs" list
PACKAGING_AUTHORS="Inq"

# If your mod depends on OpenRA.Mods.Cnc.dll from the engine set
# this to "True" to package the dll in your installers.
# Accepts values "True" or "False".
PACKAGING_COPY_CNC_DLL="True"

# If your mod depends on OpenRA.Mods.D2k.dll from the engine set
# this to "True" to package the dll in your installers.
# Accepts values "True" or "False".
PACKAGING_COPY_D2K_DLL="True"

# If you wish to enable Discord integration, register an
# application at https://discord.com/developers/applications
# and define the client id here and in your mod.yaml
PACKAGING_DISCORD_APPID="787647352399200277"

# The macOS disk image icon positions, matched to the background artwork
PACKAGING_OSX_DMG_MOD_ICON_POSITION="190, 210"
PACKAGING_OSX_DMG_APPLICATION_ICON_POSITION="410, 210"
PACKAGING_OSX_DMG_HIDDEN_ICON_POSITION="190, 350"

# Filename to use for the launcher executable on Windows.
PACKAGING_WINDOWS_LAUNCHER_NAME="CombinedArms"

# The name of the Windows Program Files directory to install the project files to.
PACKAGING_WINDOWS_INSTALL_DIR_NAME="Combined Arms"

# The key prefix used for Windows registry metadata.
# This should not contain spaces or special characters.
PACKAGING_WINDOWS_REGISTRY_KEY="CombinedArms"

# Path to the file containing the text to show in the Windows installer license dialog
PACKAGING_WINDOWS_LICENSE_FILE="./COPYING"

# Space delimited list of additional files/directories to copy from the engine directory
# when packaging your mod. e.g. "./mods/common-content"
PACKAGING_COPY_ENGINE_FILES="./mods/common-content"

# Overwrite the version in mod.yaml with the tag used for the SDK release
# Accepts values "True" or "False".
PACKAGING_OVERWRITE_MOD_VERSION="True"

##############################################################################
# Advanced Configuration
#
# Most projects will not need to modify these
##############################################################################

# Automatic engine managment will treat the OpenRA engine files like a read-only dependency.
# Disable this if you would like to modify or manager your own engine files.
AUTOMATIC_ENGINE_MANAGEMENT="True"

# The URL to download the engine files from when AUTOMATIC_ENGINE_MANAGEMENT is enabled.
AUTOMATIC_ENGINE_SOURCE="https://github.com/darkademic/OpenRA/archive/refs/heads/${ENGINE_VERSION}.zip"

# Temporary file/directory names used by automatic engine management.
# Paths outside the SDK directory are not officially supported.
AUTOMATIC_ENGINE_EXTRACT_DIRECTORY="./engine_temp"
AUTOMATIC_ENGINE_TEMP_ARCHIVE_NAME="engine.zip"
ENGINE_DIRECTORY="./engine"
