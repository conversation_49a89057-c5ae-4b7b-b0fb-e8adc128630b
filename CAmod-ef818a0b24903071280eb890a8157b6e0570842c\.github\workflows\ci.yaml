name: Continuous Integration

on:
  push:
  pull_request:

permissions:
  contents: read  #  to fetch code (actions/checkout)

jobs:
  linux:
    name: Linux (.NET 6.0)
    runs-on: ubuntu-22.04

    steps:
      - name: Remove System .NET
        run: sudo apt-get remove -y dotnet*

      - name: Clone Repository
        uses: actions/checkout@v4

      - name: Install .NET 6.0
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '6.0.x'

      - name: Prepare Environment
        run: |
          . mod.config;
          awk '/\r$$/ { exit(1); }' mod.config || (printf "Invalid mod.config format. File must be saved using unix-style (LF, not CRLF or CR) line endings.\n"; exit 1);

      - name: Check Code
        run: |
          make check
          make check-packaging-scripts

      - name: Check Mod
        run: |
          sudo apt-get install lua5.1
          make check-scripts
          make TREAT_WARNINGS_AS_ERRORS=true test

  linux-mono:
    name: Linux (mono)
    runs-on: ubuntu-22.04

    steps:
      - name: <PERSON>lone Repository
        uses: actions/checkout@v4

      - name: Prepare Environment
        run: |
          . mod.config;
          awk '/\r$$/ { exit(1); }' mod.config || (printf "Invalid mod.config format. File must be saved using unix-style (LF, not CRLF or CR) line endings.\n"; exit 1);

      - name: Check Code
        run: |
          # check-packaging-scripts does not depend on .net/mono, so is not needed here
          mono --version
          make RUNTIME=mono check

      - name: Check Mod
        run: |
          # check-scripts does not depend on .net/mono, so is not needed here
          make RUNTIME=mono TREAT_WARNINGS_AS_ERRORS=true test

  windows:
    name: Windows (.NET 6.0)
    runs-on: windows-2019

    steps:
      - name: Clone Repository
        uses: actions/checkout@v4

      - name: Install .NET 6.0
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '6.0.x'

      - name: Check Code
        shell: powershell
        run: |
          # Work around runtime failures on the GH Actions runner
          dotnet nuget add source https://api.nuget.org/v3/index.json -n nuget.org
          .\make.ps1 check

      - name: Check Mods
        run: |
          choco install lua --version ********
          $ENV:Path = $ENV:Path + ";C:\Program Files (x86)\Lua\5.1\"
          $ENV:TREAT_WARNINGS_AS_ERRORS = "true"
          .\make.ps1 check-scripts
          .\make.ps1 test
