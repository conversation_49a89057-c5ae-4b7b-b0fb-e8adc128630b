^Palettes:
	TintPostProcessEffect:

World:
	LuaScript:
		Scripts: campaign.lua, machinations.lua
	MissionData:
		Briefing: Intelligence gathered from the Nod naval base has put us hot on Nod's trail. The transports we captured were waiting to board a disguised cargo vessel which has now also been captured. Based on its navigational logs we have used our satellites and located what appears to be a research facility hidden in a disused quarry in North Africa.\n\nNod submarines will make a large scale landing difficult, but if we can establish a base on the coast we'll be able to launch an offensive from there.\n\nWith GDI's assistance we have been able to augment our harvesters and refineries to harvest the Tiberium crystals that are becoming widespread. Nod also has a harvesting operation in the area. Gather what resources you can without alerting Nod to our presence, build up your forces, then capture their research facility.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: ind

Player:
	PlayerResources:
		DefaultCash: 6000

# Disable powers for AI

^SatHackPower:
	SpawnActorPowerCA@sathack:
		Prerequisites: ~radar.nod, ~!player.legion, ~!botplayer

^NodAirdropPowers:
	ParatroopersPowerCA@NodAirDrop:
		Prerequisites: ~!botplayer

# Disable tech

TMPL:
	Inherits@CAMPAIGNDISABLED: ^Disabled

TMPP:
	Inherits@CAMPAIGNDISABLED: ^Disabled

ENLI:
	Inherits@CAMPAIGNDISABLED: ^Disabled

RMBC:
	Inherits@CAMPAIGNDISABLED: ^Disabled

# Enable subfaction specific tech

SNIP:
	Buildable:
		Prerequisites: tent, anyradar

RTNK:
	Buildable:
		Prerequisites: ~vehicles.allies

TNKD:
	Buildable:
		Prerequisites: anyradar, ~vehicles.allies

CHPR:
	Buildable:
		Prerequisites: atek, ~vehicles.allies

BATF:
	Buildable:
		Prerequisites: atek, ~vehicles.allies

NHAW:
	Buildable:
		Prerequisites: ~aircraft.allies

apb.upgrade:
	Buildable:
		Prerequisites: atek

# Misc

ATEK:
	ProduceActorPowerCA@InitialSatelliteScan:
		ChargeInterval: 15000
	ProduceActorPowerCA@SatelliteScanNoFog:
		ChargeInterval: 15000

LST.INIT:
	Inherits: LST
	RenderSprites:
		Image: lst
	RejectsOrders:
	-Selectable:
	Interactable:
	-Buildable:

BIO:
	-ProvidesPrerequisite@mortar:
	-ProvidesPrerequisite@toxintruck:
	Tooltip:
		Name: Research Lab
	Health:
		HP: 200000

TTRK:
	-Buildable:
