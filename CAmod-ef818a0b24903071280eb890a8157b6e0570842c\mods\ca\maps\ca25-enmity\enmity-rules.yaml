^Palettes:
	TintPostProcessEffect:

World:
	LuaScript:
		Scripts: campaign.lua, enmity.lua
	MissionData:
		Briefing: ———[    Chapter V Introduction    ]———\n\nThe Scrin have been rapidly growing their influence. <PERSON> has disappeared and much of his cyborg army is now under their control, along with significant concentrations of Soviet forces.\n\nThe rest of the Brotherhood has fractured. Some have joined the Scrin willingly, seeing them as the true heralds of a new age, while others cling to their belief that <PERSON> will return and continue to do battle with both us and the invaders.\n\nThe Soviet resurgence was short-lived and they are once again in disarray. <PERSON> has retreated to Siberia, and without <PERSON> their offensive capabilities are limited, however <PERSON> still refuses to negotiate a truce and cannot be relied upon to behave rationally.\n\nWe have lost much ground, but we believe there is hope. We do not think the Scrin had intended to rely so heavily on human forces to do their bidding, and are only doing so because they are unable to send for reinforcements. Their invasion force is insufficient, and their situation is more precarious than they would have us believe.\n\nThey have been consolidating territory and amassing stockpiles of Tiberium. They are preparing for something, and we need to find out what.\n\n———[    Mission Briefing    ]———\n\nNod forces continue to strike at our supply lines. Even with the survival of humanity at stake they cannot put their hatred aside. We cannot advance on the Scrin until this Nod threat is neutralized.\n\nOne route to the front line has been particularly ravaged. A detachment that was sent to secure the area has reported that they have encountered Nod scouts and they suspect another ambush is imminent. Take command of these forces and ensure they are not overwhelmed.\n\nIt seems likely that the Nod stronghold for the entire region is nearby, but we have thus far been unable to find it. If this turns out to be the case, we will send reinforcements and you are instructed to purge whatever Nod forces are present.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: trouble

Player:
	PlayerResources:
		DefaultCash: 0

recondronedetection:
	AlwaysVisible:
	Interactable:
	ScriptTriggers:
	ProvidesPrerequisite:

^Cloakable:
	Cloak@SGENCLOAK:
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Damage, Heal, SelfHeal

MSG.Static:
	Inherits: MSG
	Mobile:
		-RequireForceMoveCondition:
	RenderSprites:
		Image: msg
	WithRangeCircle@MSG:
		Range: 9c512
	ProximityExternalCondition@MSG:
		Range: 9c512
	GrantCondition@NOTMOBILE:
		Condition: notmobile
	GrantCondition@DEPLOYED:
		Condition: deployed
	-GrantConditionOnDeploy:
	WithSpriteBody@deployed:
		-RequiresCondition:
	-WithFacingSpriteBody:
	-GrantCondition@PREVIEWWORKAROUND:
	-WithMakeAnimation:
	-Tooltip:
	Tooltip@DEPLOYED:
		-RequiresCondition:

SGEN:
	WithRangeCircle@SGEN:
		Range: 14c512
	ProximityExternalCondition@SGEN:
		Range: 14c512

UAV:
	DetectCloaked:
		RequiresCondition: has-detection
		Range: 7c0
	GrantConditionOnPrerequisite@DETECTION:
		Condition: has-detection
		Prerequisites: recondronedetection
	-Targetable@AIRBORNE:

HQ:
	AirstrikePowerCA@uav:
		Description: \nA drone flies across the map, revealing the area as it passes.

EYE:
	Inherits@CAMPAIGNDISABLED: ^Disabled

ORCA:
	Buildable:
		Prerequisites: ~aircraft.gdi

HPAD.TD:
	-InterceptorPower@AirDef:
