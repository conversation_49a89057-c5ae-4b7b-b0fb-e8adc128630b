
World:
	LuaScript:
		Scripts: composition-tournament.lua
	MissionData:
		Briefing: ================\n\n- All units/defenses can be built instantly.\n-------------------------\n- Players are pitted against each other in sealed off arenas.\n-------------------------\n- Every player plays every other player at least once.\n-------------------------\n- Capture the command center to win a matchup.\n-------------------------\n- Winner is the player with the most matchup victories.\n-------------------------\n- Do not surrender (this will remove you from subsequent rounds).\n\n================\n
	ScriptLobbyDropdown@cycles:
		ID: numcycles
		Label: No. of Round Robins
		Description: How many times each player plays every other player
		Values:
			1: 1
			2: 2
			3: 3
			4: 4
			5: 5
			6: 6
			7: 7
			8: 8
			9: 9
			10: 10
		Default: 3

Player:
	PlayerResources:
		SelectableCash: 5000, 7500, 10000, 15000, 20000, 25000, 30000, 35000, 40000, 45000, 50000, 75000, 100000, 200000
	LobbyPrerequisiteCheckbox@SATHACK:
		ID: satscan
		Label: Satscan Enabled
		Description: Players can periodically use a satscan power
		Enabled: True
		DisplayOrder: 999
		Prerequisites: global.satscan
	SquadManagerBotModuleCA@normal:
		SquadValue: 100
		SquadValueRandomBonus: 0
		SquadSize: 1
		SquadSizeRandomBonus: 0
		AirSquadTargetArmorTypes:
			heli: Aircraft, Heavy
			nhaw: None
			hind: None, Heavy, Light
			yak: Wood, None, Aircraft, Light
			mig: Aircraft, Heavy, Light, Concrete
			pmak: Heavy, Concrete
			beag: Aircraft, Heavy, Light, Concrete
			suk: Heavy, Concrete
			suk.upg: Heavy, Wood
			kiro: Wood
			orca: Aircraft, Heavy
			orcb: Heavy, Light, Concrete
			a10: None, Wood
			a10.sw: None, Wood, Aircraft
			a10.gau: None, Wood, Light
			auro: Heavy, Concrete, Wood
			jack: Heavy, Light, Concrete
			apch: None, Wood, Aircraft, Light
			venm: None, Aircraft, Light
			rah: None, Wood, Light
			harr: None, Wood, Aircraft, Light
			scrn: Aircraft, Heavy, Concrete
			stmr: Aircraft, None, Wood, Light
			torm: Aircraft, Heavy
			enrv: Aircraft, Heavy, Concrete
			mshp: Wood
	CaptureManagerBotModuleCA:
		CapturableActorTypes: miss
	-ModularBot@BrutalAI:
	-ModularBot@VeryHardAI:
	-ModularBot@HardAI:
	-ModularBot@EasyAI:
	-ModularBot@NavalAI:

playerutils:
	AlwaysVisible:
	Interactable:
	ScriptTriggers:
	EditorOnlyTooltip:
		Name: (player utils)
	Immobile:
		OccupiesSpace: false
	WithSpriteBody:
	RenderSpritesEditorOnly:
		Image: waypoint
	BodyOrientation:
		QuantizedFacings: 1
	MapEditorData:
		Categories: System
	ProvidesRadar:
	Power:
		Amount: 3000
	MustBeDestroyed:
		RequiredForShortGame: true
	StoresPlayerResources:
		Capacity: 500000
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active
	SpawnActorPowerCA@sathack:
		Actor: camera.sathack
		Prerequisites: global.satscan
		LifeTime: 120
		OrderName: sathack
		Icon: hacksat
		ChargeInterval: 2250
		Name: Satscan
		Description: \nReveals the targeted area for a short time.
		LaunchSound: hacksat.aud
		SelectTargetSpeechNotification: SelectTarget
		SelectTargetTextNotification: Select target.
		EffectImage: empty
		EffectSequence: idle
		EffectPalette: tseffect-ignore-lighting-alpha75
		TargetCircleRange: 9c512
		TargetCircleColor: 999999AA
		SupportPowerPaletteOrder: 50
		StartFullyCharged: true

basebuilder:
	AlwaysVisible:
	Interactable:
	ScriptTriggers:
	EditorOnlyTooltip:
		Name: (basebuilder)
	Building:
		Dimensions: 1,1
		Footprint: x
		TerrainTypes: Clear,Road
	WithSpriteBody:
	RenderSpritesEditorOnly:
		Image: waypoint
	BaseProvider:
		Range: 20c0
	GivesBuildableArea:
		AreaTypes: static

spectatorcam:
	AlwaysVisible:
	Interactable:
	ScriptTriggers:
	RevealsMap:
		RevealGeneratedShroud: true
		ValidRelationships: Ally

^Defense:
	RequiresBuildableArea:
		Adjacent: 22
		AreaTypes: static

E6:
	Valued:
		Cost: 0
	Captures:
		CaptureDelay: 325
	Buildable:
		BuildLimit: 1
		Prerequisites: ~player.ra, !engi
	ProvidesPrerequisite@ENGI:
		Prerequisite: engi
	Mobile:
		Speed: 90

N6:
	Valued:
		Cost: 0
	Captures:
		CaptureDelay: 325
	Buildable:
		BuildLimit: 1
		Prerequisites: ~player.td, !engi
	ProvidesPrerequisite@ENGI:
		Prerequisite: engi
	Mobile:
		Speed: 90

S6:
	Valued:
		Cost: 0
	Captures:
		CaptureDelay: 325
	Buildable:
		BuildLimit: 1
		Prerequisites: ~player.scrin, !engi
	ProvidesPrerequisite@ENGI:
		Prerequisite: engi
	Mobile:
		Speed: 90

MISS:
	DamageMultiplier@1:
		Modifier: 0
	Capturable:
		RequiresCondition: !locked
	ExternalCondition@LOCKED:
		Condition: locked
	Tooltip:
		Name: Arena Command Center
	TooltipDescription@ally:
		Description: Capture this to win the matchup.
	TooltipDescription@other:
		Description: Capture this to win the matchup.

FIX:
	Inherits@UPG: ^ProducesUpgrades
	Production@SQBLD:
		Produces: DefenseSQ
		PauseOnCondition: forceshield || invulnerability || being-warped
	ProductionBar@SQDEF:
		ProductionType: DefenseSQ
		Color: 8A8A8A

AGUN.AI:
	Inherits: AGUN
	RenderSprites:
		Image: agun
	-Targetable:
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None
	Power:
		Amount: 0
	-Buildable:
	Armament:
		Weapon: ZSU-23-AI

FACT:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

WEAP:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

TENT:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

AFLD:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

DOME:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

FIX:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-GrantConditionOnResupplying@Resupplying:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

NUK2:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

SYRD:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

CTNK:
	PortableChronoCA:
		MaxDistance: 10

CHPR:
	PortableChronoCA:
		MaxDistance: 10

S4:
	PortableChronoCA:
		MaxDistance: 10

MCV:
	Buildable:
		Prerequisites: ~disabled

AMCV:
	Buildable:
		Prerequisites: ~disabled

SMCV:
	Buildable:
		Prerequisites: ~disabled

MSHP:
	SpawnActorAbility:
		Range: 10c0

AFLD:
	-AirstrikePowerCA@Russianparabombs:
	-AirstrikePowerCA@CarpetBomb:
	-AirstrikePowerCA@Iraqiparabombs:
	-AirstrikePowerCA@ChaosBombs:

DOME:
	-AirstrikePowerCA@clustermines:
	-SpawnActorPowerCA@VeilOfWar:
	-ParatroopersPowerCA@paratroopers:
	-ParatroopersPowerCA@Russianparatroopers:
	-AirstrikePowerCA@MutaBomb:
	-AirstrikePowerCA@spyplane:

E7:
	Buildable:
		Prerequisites: ~player.allies, !gunhero
	ProvidesPrerequisite@GUNHERO:
		Prerequisite: gunhero

BORI:
	Buildable:
		Prerequisites: ~player.soviet, !gunhero
	ProvidesPrerequisite@GUNHERO:
		Prerequisite: gunhero

RMBO:
	Buildable:
		Prerequisites: ~player.td, !gunhero
	ProvidesPrerequisite@GUNHERO:
		Prerequisite: gunhero

YURI:
	Buildable:
		Prerequisites: ~player.soviet, !mchero
	ProvidesPrerequisite@MCHERO:
		Prerequisite: mchero

MAST:
	Buildable:
		Prerequisites: ~player.scrin, !mchero
	ProvidesPrerequisite@MCHERO:
		Prerequisite: mchero

# ####

hazmat.upgrade:
	Buildable:
		Prerequisites: ~disabled

hazmatsoviet.upgrade:
	Buildable:
		Prerequisites: ~disabled

U3.squad:
	Buildable:
		Prerequisites: ~disabled

GTNK.squad:
	Buildable:
		Prerequisites: ~disabled
