Container@DISPLAY_PANEL:
	Logic: DisplaySettingsLogic
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Children:
		Scroll<PERSON>anel@SETTINGS_SCROLLPANEL:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			CollapseHiddenChildren: True
			TopBottomSpacing: 5
			ItemSpacing: 10
			Children:
				Background@PROFILE_SECTION_HEADER:
					X: 5
					Width: PARENT_WIDTH - 24 - 10
					Height: 13
					Background: separator
					ClickThrough: True
					Children:
						Label@LABEL:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Font: TinyBold
							Align: Center
							Text: Profile
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@PLAYER_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								LabelForInput@PLAYER:
									Width: PARENT_WIDTH
									Height: 20
									Text: Player Name:
									For: <PERSON>LAY<PERSON><PERSON><PERSON>
								TextField@PLAYERNAME:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
									MaxLength: 16
									Text: Name
						Container@PLAYERCOLOR_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								LabelForInput@COLOR:
									Width: PARENT_WIDTH
									Height: 20
									Text: Preferred Color:
									For: PLAYERCOLOR
								DropDownButton@PLAYERCOLOR:
									Y: 25
									Width: 75
									Height: 25
									IgnoreChildMouseOver: true
									PanelAlign: Right
									Children:
										ColorBlock@COLORBLOCK:
											X: 5
											Y: 6
											Width: PARENT_WIDTH - 35
											Height: PARENT_HEIGHT - 12
				Container@SPACER:
				Background@DISPLAY_SECTION_HEADER:
					X: 5
					Width: PARENT_WIDTH - 24 - 10
					Height: 13
					Background: separator
					ClickThrough: True
					Children:
						Label@LABEL:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Font: TinyBold
							Align: Center
							Text: Display
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@BATTLEFIELD_CAMERA_DROPDOWN_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@BATTLEFIELD_CAMERA:
									Width: PARENT_WIDTH
									Height: 20
									Text: Battlefield Camera:
								DropDownButton@BATTLEFIELD_CAMERA_DROPDOWN:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
									Font: Regular
						Container@TARGET_LINES_DROPDOWN_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@TARGET_LINES:
									Width: PARENT_WIDTH
									Height: 20
									Text: Target Lines:
								DropDownButton@TARGET_LINES_DROPDOWN:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
									Font: Regular
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@UI_SCALE_DROPDOWN_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								LabelForInput@UI_SCALE:
									Width: PARENT_WIDTH
									Height: 20
									Text: UI Scale:
									For: UI_SCALE_DROPDOWN
								DropDownButton@UI_SCALE_DROPDOWN:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
									Font: Regular
						Container@STATUS_BAR_DROPDOWN_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@STATUS_BARS:
									Width: PARENT_WIDTH
									Height: 20
									Text: Status Bars:
								DropDownButton@STATUS_BAR_DROPDOWN:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
									Font: Regular
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@CURSORDOUBLE_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@CURSORDOUBLE_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Increase Cursor Size
						Container@PLAYER_STANCE_COLORS_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@PLAYER_STANCE_COLORS_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Player Relationship Colors
									TooltipText: Change minimap and health bar colors based on relationship (own, enemy, ally, neutral)
									TooltipContainer: SETTINGS_TOOLTIP_CONTAINER
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@UI_FEEDBACK_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 10
							Children:
								Checkbox@UI_FEEDBACK_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Show UI Feedback Notifications
									TooltipText: Show transient text notifications for UI events
									TooltipContainer: SETTINGS_TOOLTIP_CONTAINER
						Container@TRANSIENTS_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@TRANSIENTS_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Show Game Event Notifications
									TooltipText: Show transient text notifications for game events
									TooltipContainer: SETTINGS_TOOLTIP_CONTAINER
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@PAUSE_SHELLMAP_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 10
							Children:
								Checkbox@PAUSE_SHELLMAP_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Pause Menu Background
						Container@HIDE_REPLAY_CHAT_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 10
							Children:
								Checkbox@HIDE_REPLAY_CHAT_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Hide Chat in Replays
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@SELECTIONTOOLTIP_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 10
							Children:
								Checkbox@SELECTIONTOOLTIP_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Selected Unit Tooltip
									TooltipText: When a single unit/structure is selected, show info about it in bottom right corner
									TooltipContainer: SETTINGS_TOOLTIP_CONTAINER
				Container@SPACER:
				Background@VIDEO_SECTION_HEADER:
					X: 5
					Width: PARENT_WIDTH - 24 - 10
					Height: 13
					Background: separator
					ClickThrough: True
					Children:
						Label@LABEL:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Font: TinyBold
							Align: Center
							Text: Video
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@VIDEO_MODE_DROPDOWN_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@VIDEO_MODE:
									Width: PARENT_WIDTH
									Height: 20
									Text: Video Mode:
								DropDownButton@MODE_DROPDOWN:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
									Font: Regular
									Text: Windowed
						Container@WINDOW_RESOLUTION_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@WINDOW_SIZE:
									Width: PARENT_WIDTH
									Height: 20
									Text: Window Size:
								TextField@WINDOW_WIDTH:
									Y: 25
									Width: 55
									Height: 25
									MaxLength: 5
									Type: Integer
								Label@X:
									X: 55
									Y: 25
									Text: x
									Font: Bold
									Height: 25
									Width: 15
									Align: Center
								TextField@WINDOW_HEIGHT:
									X: 70
									Y: 25
									Width: 55
									Height: 25
									MaxLength: 5
									Type: Integer
						Container@DISPLAY_SELECTION_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@DISPLAY_SELECTION_LABEL:
									Width: PARENT_WIDTH
									Height: 20
									Text: Select Display:
								DropDownButton@DISPLAY_SELECTION_DROPDOWN:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
									Font: Regular
									Text: Standard
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@FRAME_LIMIT_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@FRAME_LIMIT_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Enable Frame Limiter
						Container@FRAME_LIMIT_SLIDER_CONTAINER:
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Slider@FRAME_LIMIT_SLIDER:
									X: 20
									Y: 25
									Width: PARENT_WIDTH - 20
									Height: 20
									Ticks: 20
									MinimumValue: 50
									MaximumValue: 240
						Container@VSYNC_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@VSYNC_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Enable VSync
						Container@FRAME_LIMIT_GAMESPEED_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Y: 25
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@FRAME_LIMIT_GAMESPEED_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: Limit framerate to game tick rate
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@GL_PROFILE_DROPDOWN_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@GL_PROFILE:
									Width: PARENT_WIDTH
									Height: 20
									Text: OpenGL Profile:
								DropDownButton@GL_PROFILE_DROPDOWN:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
									Font: Regular
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 30
					Children:
						Container@RESTART_REQUIRED_CONTAINER:
							X: 10
							Width: PARENT_WIDTH - 20
							Children:
								Label@VIDEO_RESTART_REQUIRED_DESC:
									Width: PARENT_WIDTH
									Height: 20
									Font: Tiny
									Text: Display and OpenGL changes require restart
									Align: Center
