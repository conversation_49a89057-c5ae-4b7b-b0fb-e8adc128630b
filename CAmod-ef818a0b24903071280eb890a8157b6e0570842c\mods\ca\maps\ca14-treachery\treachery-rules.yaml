^Palettes:
	TintPostProcessEffect:

World:
	LuaScript:
		Scripts: campaign.lua, treachery.lua
	MissionData:
		Briefing: We have been betrayed! Our recent losses were enough for the coward General <PERSON><PERSON><PERSON> to show his true colours. He, along with an unknown number of troops loyal to him, have defected to the Allies.\n\nHe was not counting on hostilities with the Brotherhood of Nod ending so soon, and must now be feeling very nervous.\n\nHe deserves no mercy. Put an end to the traitor and anyone who tries to protect him.\n\n<PERSON><PERSON> believes he can kill the traitor by himself. If you don't share his confidence then the safer approach would be to find our abandoned base, build up a force, and overwhelm <PERSON><PERSON><PERSON>'s defenders.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: terminat

Player:
	PlayerResources:
		DefaultCash: 6000

# Disable powers for AI

^SpyPlanePower:
	AirstrikePowerCA@spyplane:
		Prerequisites: ~!botplayer

^ParabombsPower:
	AirstrikePowerCA@Russianparabombs:
		Prerequisites: ~support.parabombs, ~!botplayer

^ParatroopersPower:
	ParatroopersPowerCA@paratroopers:
		Prerequisites: ~support.paratroopers, ~!botplayer

# Disable tech

MCV:
	Inherits@CAMPAIGNDISABLED: ^Disabled

MSLO:
	Inherits@CAMPAIGNDISABLED: ^Disabled

DOME:
	-ParatroopersPowerCA@paratroopers:
	AirstrikePowerCA@spyplane:
		ChargeInterval: 4500

PROC:
	StoresPlayerResources:
		Capacity: 30000

HPAD.Abandoned:
	Inherits: HPAD
	RenderSprites:
		Image: hpad
	Exit@1:
		Facing: 384
	Power:
		Amount: 0
	-AirstrikePowerCA@Russianparabombs:
	-RallyPoint:
	-FreeActor@QUEUEUPDATER:
	-GrantDelayedCondition@QUEUEUPDATER:
	-GrantConditionOnPrerequisite@MQF:
	-GrantConditionOnPrerequisite@MQS:
	-PrimaryBuilding:
	-WithDecoration@primary:
	-ProductionQueue@MQAIR:
	-Production@SQAIR:
	-Production@MQAIR:
	-ProductionBar@SQAIR:
	-ProductionBar@MQAIR:

HALO:
	GrantConditionIfOwnerIsNeutral@NEUTRAL:
		Condition: is-neutral
	WithIdleOverlay@ROTOR1GROUND:
		PauseOnCondition: is-neutral
	RevealsShroud:
		Range: 10c0

BORI:
	RevealsShroud:
		Range: 8c0
	-Demolition:
	AutoTarget:
		InitialStance: HoldFire
	-Buildable:
	-ChangesHealth@CommandoRegen:

GNRL:
	Tooltip:
		Name: General Yegorov
	RenderSprites:
		Image: boris
		-Palette:
		PlayerPalette: playertd
	WithDeathAnimation:
		DeathSequencePalette: playertd
	GrantTimedCondition@IMPERVIOUS:
		Condition: impervious
		Duration: 15
	DamageMultiplier@IMPERVIOUS:
		Modifier: 0
		RequiresCondition: impervious
	-Valued:
	-AttackFrontal:
	-AttackMove:
	-Guard:
	-GrantConditionOnFaction@RA:
	-GrantConditionOnFaction@TD:
	-Armament@RA:
	-Armament@TD:
	-AutoTarget:
	-AutoTargetPriority@DEFAULT:
	-AutoTargetPriority@ATTACKANYTHING:

FCOM:
	Health:
		HP: 200000
	Tooltip:
		Name: General Yegorov's HQ
		-GenericVisibility:
	-BaseProvider:
	-SpawnActorOnDeath:
	RepairableBuilding:
		RepairStep: 500
		RepairPercent: 30
		RepairingNotification: Repairing

powerproxy.halodrop:
	ParatroopersPower:
		DisplayBeacon: False
		DropItems: E1, E1, E3, E2, E1, E1, E3, E8, E1
		UnitType: halo.paradrop.invuln
	AlwaysVisible:

powerproxy.shockdrop:
	ParatroopersPower:
		DisplayBeacon: False
		DropItems: SHOK, SHOK, SHOK, SHOK, SHOK
		UnitType: halo.paradrop.invuln
	AlwaysVisible:

HALO.paradrop.invuln:
	Inherits: HALO.paradrop
	-Targetable@AIRBORNE:

# Hunt() requires only 1 AttackBase
BATF.AI:
	-AttackFrontal:
