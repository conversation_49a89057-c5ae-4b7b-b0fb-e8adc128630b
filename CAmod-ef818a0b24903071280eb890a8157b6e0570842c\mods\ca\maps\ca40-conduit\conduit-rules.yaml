
^Palettes:
	TintPostProcessEffect:

^BaseWorld:
	TerrainLighting:

World:
	LuaScript:
		Scripts: campaign.lua, conduit.lua
	MissionData:
		Briefing: Much to our surprise, a Scrin creature of immense psionic power—the so-called Prodigy who usurped our control of <PERSON>'s cyborgs—had been imprisoned alongside <PERSON>.\n\n<PERSON>hile incarcerated, <PERSON> was able to communicate with this creature and he believes that joining forces with its master, the Scrin Overlord, could be of great mutual benefit.\n\nIf GDI sources are to be believed, the terrorist <PERSON> has caused the Overlord much grief; inciting a civil war and threatening the Overlord's millennia long grip on power. Our assistance at this pivotal moment would make the Overlord indebted to us, and we would surely be greatly rewarded.\n\n<PERSON><PERSON>'s delusional crusade must be stopped. We must seize this opportunity, and bring peace and stability back to not one, but two civilizations. With the Overlord's support, we will have the power to destroy our enemies once and for all, and an era of Soviet supremacy will finally be upon us.\n\nAs multiple gateways to their homeworld remain open, the Scrin Prodigy has been able to communicate with his master. The loyalists have suffered a major defeat, and it seems <PERSON> is preparing to return to Earth with his vast cyborg army, while the Scrin rebels continue to eat away at the Overlord's remaining forces.\n\nOur goal is simple; assault the Nod base where the gateway has been prepared for <PERSON>'s return, and cut off his escape route. In the meantime, preparations are underway for our own forces to travel through the gateway, so that we may turn the tide of the war in the Overlord's favour.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: indeep

Player:
	PlayerResources:
		DefaultCash: 6000

NodGateway:
	Inherits: ^WormholeBase
	Tooltip:
		Name: Gateway
	RenderSprites:
		Image: wormholexl
	MapEditorData:
		Categories: System
	TerrainLightSource:
		Range: 3c0
		Intensity: 0.5
		RedTint: 1

substrike.spawner:
	Inherits: ^WeaponDetonaterDummy
	PeriodicExplosion:
		Weapon: SubterraneanStrikeSpawner
	EditorOnlyTooltip:
		Name: Subterranean Strike Spawner
