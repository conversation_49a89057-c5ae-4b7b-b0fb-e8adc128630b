aftermath: Aftermath Expansion Disc (English)
	Type: Disc
	IDFiles:
		README.TXT: 9902fb74c019df1b76ff5634e68f0371d790b5e0
		SETUP/INSTALL/PATCH.RTP: 5bce93f834f9322ddaa7233242e5b6c7fea0bf17
	Install:
		# Aftermath expansion files:
		ContentPackage@aftermathbase:
			Name: aftermathbase
			Actions:
				ExtractRaw: SETUP/INSTALL/PATCH.RTP
					^SupportDir|Content/ca/expand/expand2.mix:
						Offset: 4712984
						Length: 469922
					^SupportDir|Content/ca/expand/hires1.mix:
						Offset: 5182981
						Length: 90264
					^SupportDir|Content/ca/expand/lores1.mix:
						Offset: 5273320
						Length: 57076
				ExtractMix@1: MAIN.MIX
					^SupportDir|Content/ca/expand/sounds.mix: sounds.mix
				ExtractMix@2: ^SupportDir|Content/ca/expand/sounds.mix
					^SupportDir|Content/ca/expand/chrotnk1.aud: chrotnk1.aud
					^SupportDir|Content/ca/expand/fixit1.aud: fixit1.aud
					^SupportDir|Content/ca/expand/jburn1.aud: jburn1.aud
					^SupportDir|Content/ca/expand/jchrge1.aud: jchrge1.aud
					^SupportDir|Content/ca/expand/jcrisp1.aud: jcrisp1.aud
					^SupportDir|Content/ca/expand/jdance1.aud: jdance1.aud
					^SupportDir|Content/ca/expand/jjuice1.aud: jjuice1.aud
					^SupportDir|Content/ca/expand/jjump1.aud: jjump1.aud
					^SupportDir|Content/ca/expand/jlight1.aud: jlight1.aud
					^SupportDir|Content/ca/expand/jpower1.aud: jpower1.aud
					^SupportDir|Content/ca/expand/jshock1.aud: jshock1.aud
					^SupportDir|Content/ca/expand/jyes1.aud: jyes1.aud
					^SupportDir|Content/ca/expand/madchrg2.aud: madchrg2.aud
					^SupportDir|Content/ca/expand/madexplo.aud: madexplo.aud
					^SupportDir|Content/ca/expand/mboss1.aud: mboss1.aud
					^SupportDir|Content/ca/expand/mhear1.aud: mhear1.aud
					^SupportDir|Content/ca/expand/mhotdig1.aud: mhotdig1.aud
					^SupportDir|Content/ca/expand/mhowdy1.aud: mhowdy1.aud
					^SupportDir|Content/ca/expand/mhuh1.aud: mhuh1.aud
					^SupportDir|Content/ca/expand/mlaff1.aud: mlaff1.aud
					^SupportDir|Content/ca/expand/mrise1.aud: mrise1.aud
					^SupportDir|Content/ca/expand/mwrench1.aud: mwrench1.aud
					^SupportDir|Content/ca/expand/myeehaw1.aud: myeehaw1.aud
					^SupportDir|Content/ca/expand/myes1.aud: myes1.aud
				Delete: ^SupportDir|Content/ca/expand/sounds.mix
		# Aftermath music (optional):
		ContentPackage@music-aftermath:
			Name: music-aftermath
			Actions:
				ExtractMix@1: MAIN.MIX
					^SupportDir|Content/ca/expand/scores.mix: scores.mix
				ExtractMix@2: ^SupportDir|Content/ca/expand/scores.mix
					^SupportDir|Content/ca/expand/await.aud: await.aud
					^SupportDir|Content/ca/expand/bog.aud: bog.aud
					^SupportDir|Content/ca/expand/float_v2.aud: float_v2.aud
					^SupportDir|Content/ca/expand/gloom_ra.aud: gloom.aud
					^SupportDir|Content/ca/expand/grndwire.aud: grndwire.aud
					^SupportDir|Content/ca/expand/rpt.aud: rpt.aud
					^SupportDir|Content/ca/expand/search.aud: search.aud
					^SupportDir|Content/ca/expand/traction.aud: traction.aud
					^SupportDir|Content/ca/expand/wastelnd.aud: wastelnd.aud
				Delete: ^SupportDir|Content/ca/expand/scores.mix
