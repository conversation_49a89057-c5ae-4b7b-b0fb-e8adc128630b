^Palettes:
	TintPostProcessEffect:
	PaletteFromFile@terrain-temperat:
		Filename: ca|maps/ca22-decimation/fall.pal

World:
	LuaScript:
		Scripts: campaign.lua, emancipation.lua
	MissionData:
		Briefing: A disaster has occurred, commander. Several of our bases near the front line, which we have been using as staging areas for our final assault, have fallen to Scrin mind control.\n\nWe have encountered the Scrin Masterminds before, but we had no idea they were capable of anything on this scale.\n\nOur Advanced Robotics Command has begun mass mobilisation of drone units, as we cannot risk sending any forces that are vulnerable to mind control.\n\nYour mission is to take some of these drone units and use them to neutralise the Scrin Masterminds. Although we cannot be sure, we think that killing a Mastermind will release all of the minds it has enslaved.\n\nTake care not to kill GDI forces that are under Scrin control. Once you have regained control of all of our bases, destroy the Scrin presence in the area.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: march

Player:
	PlayerResources:
		DefaultCash: 0

# Disable powers for AI

^InterceptorsPower:
	InterceptorPower@AirDef:
		Prerequisites: ~aircraft.gdi, ~!botplayer

amcv.enabled:
	AlwaysVisible:
	Interactable:
	ScriptTriggers:
	ProvidesPrerequisite:

AMCV:
	Buildable:
		Prerequisites: vehicles.mcv, ~amcv.enabled, ~vehicles.td

MAST:
	MindController:
		Capacity: 0
	GrantCondition@mindcontrolling:
		Condition: mindcontrolling
	-WithDecoration@COMMANDOSKULL:
	-ChangesHealth@CommandoRegen:

MSHP:
	Inherits@CAMPAIGNDISABLED: ^Disabled

MSAR:
	MustBeDestroyed:
		RequiredForShortGame: true

EYE:
	DetonateWeaponPower@IonStorm:
		ChargeInterval: 18000

GDRN:
	RevealsShroud:
		Range: 8c0
	Buildable:
		Prerequisites: ~vehicles.gdi, ~!tow.upgrade

GDRN.TOW:
	Buildable:
		Prerequisites: ~vehicles.gdi, ~tow.upgrade

MDRN:
	Buildable:
		Prerequisites: anyradar, ~vehicles.gdi

UAV:
	-Targetable@AIRBORNE:
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
	RevealsShroud@GAPGEN:
		Range: 10c0

bdrone.upgrade:
	-Buildable:
	-AnnounceOnCreation:

mdrone.upgrade:
	-Buildable:
	-AnnounceOnCreation:

# Hunt() requires only 1 AttackBase
DEVA:
	-AttackFrontalCharged:
	-Armament@PRIMARYUPG:
	-AmbientSound@CHARGE:
	-WithIdleOverlay@CHARGE1:
	-WithIdleOverlay@CHARGE2:
	-WithIdleOverlay@CHARGE3:
