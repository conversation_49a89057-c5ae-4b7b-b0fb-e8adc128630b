
^ScrinBuilding:
	Inherits@1: ^Building
	RenderSprites:
		PlayerPalette: playerscrin
	SpawnActorOnDeath:
		Actor: s1
	SpawnActorsOnSellCA:
		ActorTypes: s1
		GuaranteedActorTypes: s1, s1
	-SpawnRandomActorOnDeath:
	Building:
		BuildSounds: scrinbuild.aud

INFANTRY.SCRIN:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Infantry Production
	Buildable:
		Description: Infantry Production

VEHICLES.SCRIN:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Vehicle Production
	Buildable:
		Description: Vehicle Production

^OnResourceConditions:
	ExternalCondition@RESCONVTIB:
		Condition: on-tib
	ExternalCondition@RESCONVORE:
		Condition: on-ore
	ExternalCondition@RESCONVBLUETIB:
		Condition: on-bluetib
	ExternalCondition@RESCONVGEMS:
		Condition: on-gems
	GrantCondition@RESCONV:
		Condition: on-resource
		RequiresCondition: on-tib || on-ore || on-bluetib || on-gems
	GrantConditionOnTerrain@RESCONVTIB:
		Condition: on-tib
		TerrainTypes: Tiberium
	GrantConditionOnTerrain@RESCONVORE:
		Condition: on-ore
		TerrainTypes: Ore
	GrantConditionOnTerrain@RESCONVBLUETIB:
		Condition: on-bluetib
		TerrainTypes: BlueTiberium
	GrantConditionOnTerrain@RESCONVGEMS:
		Condition: on-gems
		TerrainTypes: Gems

^ResourceConversion:
	Inherits: ^OnResourceConditions
	AmmoPool@RESCONV:
		Ammo: 5
		InitialAmmo: 0
		AmmoCondition: ammo
		Armaments: primary-charged
	ReloadAmmoPool@RESCONV:
		Count: 1
		Delay: 25
		RequiresCondition: charging-on-resource
	WithAmmoPipsDecoration@RESCONV:
		PipCount: 5
		RequiresCondition: ammo
		RequiresSelection: true
		Position: BottomLeft
		Margin: 4, 3
	GrantCondition@RESCONVCHARGE:
		Condition: charging-on-resource
		RequiresCondition: resconv-upgrade && on-resource && (ammo < 5 || charged-fire)
	GrantConditionOnAttack@PRIMARY:
		ArmamentNames: primary
		Condition: uncharged-fire
		RevokeDelay: 85
	GrantConditionOnAttack@PRIMARYCHARGED:
		ArmamentNames: primary-charged
		Condition: charged-fire
		RevokeDelay: 85
	GrantConditionOnPrerequisite@RESCONV:
		Condition: resconv-upgrade
		Prerequisites: resconv.upgrade
	WithIdleOverlay@RESCONVTIB:
		Image: resconv
		Sequence: green
		Palette: scrin
		RequiresCondition: on-tib && charging-on-resource
		Offset: 0,0,300
	WithIdleOverlay@RESCONVORE:
		Image: resconv
		Sequence: yellow
		Palette: scrin
		RequiresCondition: on-ore && charging-on-resource
		Offset: 0,0,300
	WithIdleOverlay@RESCONVBLUETIB:
		Image: resconv
		Sequence: blue
		Palette: scrin
		RequiresCondition: on-bluetib && charging-on-resource
		Offset: 0,0,300
	WithIdleOverlay@RESCONVGEMS:
		Image: resconv
		Sequence: red
		Palette: scrin
		RequiresCondition: on-gems && charging-on-resource
		Offset: 0,0,300

^HullRegen:
	GrantConditionOnPrerequisite@REGEN:
		Condition: regen-upgrade
		Prerequisites: regen.upgrade
	ChangesHealth@REGEN1:
		Step: 400
		Delay: 25
		StartIfBelow: 66
		DamageCooldown: 250
		RequiresCondition: regen-upgrade
	ChangesHealth@REGEN2:
		Step: 200
		Delay: 25
		StartIfBelow: 100
		DamageCooldown: 250
		RequiresCondition: regen-upgrade

^ScrinShields:
	Shielded:
		MaxStrength: 8000
		RegenDelay: 350
		RegenAmount: 250
		RegenInterval: 5
		ShieldsUpCondition: shields-up
		RequiresCondition: shields-upgrade && !empdisable
		SelectionBarColor: ead6ff
	Targetable@SHIELDS:
		TargetTypes: Shielded
		RequiresCondition: shields-up
	GrantConditionOnPrerequisite@SHIELDS:
		Condition: shields-upgrade
		Prerequisites: shields.upgrade
	WithFacingSpriteBody@SHIELDS:
		Name: shield
		Sequence: shield
		Palette: scrinshield-ignore-lighting-alpha50
		RequiresCondition: shields-upgrade && shields-up
	SpeedMultiplier@SHIELDWARP:
		Modifier: 25
		RequiresCondition: shield-warped
	ExternalCondition@SHIELDWARP:
		Condition: shield-warped
	WithColoredOverlay@SHIELDWARP:
		RequiresCondition: shield-warped
		Color: 51aeda38
	WithIdleOverlay@TEMPORAL:
		RequiresCondition: being-warped || shield-warped

^MothershipRearmable:
	Targetable@MothershipRearmable:
		TargetTypes: MothershipRearmable
		RequiresCondition: ammo < 15
	ReloadAmmoPool@MothershipRearmable:
		Count: 3
		Delay: 75
		RequiresCondition: mshp-rearm
	ExternalCondition@MothershipRearmable:
		Condition: mshp-rearm

^FleetRecallable:
	Targetable@FleetRecallable:
		TargetTypes: FleetRecallable

^SignalTransmitterSpeedBoost:
	GrantConditionOnPrerequisite@SignalTransmitterSpeedBoost:
		Condition: sign-speed
		Prerequisites: sign
	SpeedMultiplier@SignalTransmitterSpeedBoost:
		Modifier: 110
		RequiresCondition: sign-speed

^IonConduits:
	GrantConditionOnPrerequisite@IONCON:
		Condition: ioncon-upgrade
		Prerequisites: ioncon.upgrade
	Targetable@StormBoost:
		TargetTypes: StormBoost
	AmbientSoundCA@IONSTORM:
		SoundFiles: ionstorm.aud
		Delay: 0
		Interval: 850
		VolumeMultiplier: 0.4
		RequiresCondition: storm-level
	AmbientSoundCA@AMBIENCE:
		SoundFiles: thunder1.aud, thunder2.aud, thunder3.aud, thunder4.aud, thunder5.aud, thunder.ambient.aud
		Delay: 20, 200
		Interval: 250, 1250
		VolumeMultiplier: 0.3
		RequiresCondition: storm-level
	LaysMinefield@IONSTORM:
		Mines: strm, strm, strm.live
		MineSelectionMode: Shuffled
		Locations: 0,1, 1,0, -1,0
		RecreationInterval: 150
		RequiresCondition: storm-level
	GrantConditionOnAttackCA@IONSTORM:
		Condition: storm-active
		RevokeDelay: 75
		RequiresCondition: ioncon-upgrade
	GrantConditionOnDamage@IONSTORM:
		Condition: storm-active
		Duration: 75
		RequiresCondition: ioncon-upgrade && !empdisable
		ValidRelationships: Ally, Enemy, Neutral
	GrantStackingCondition@IONSTORM:
		Condition: storm-level
		DelayPerInstance: 120
		MaximumInstances: 3
		RevokeDelay: 200
		RequiresCondition: storm-active
	RangeMultiplier@IONSTORM1:
		Modifier: 105
		RequiresCondition: storm-level == 1
	FirepowerMultiplier@IONSTORM1:
		Modifier: 110
		RequiresCondition: storm-level == 1
	DamageMultiplier@IONSTORM1:
		Modifier: 95
		RequiresCondition: storm-level == 1
	RangeMultiplier@IONSTORM2:
		Modifier: 110
		RequiresCondition: storm-level == 2
	FirepowerMultiplier@IONSTORM2:
		Modifier: 115
		RequiresCondition: storm-level == 2
	DamageMultiplier@IONSTORM2:
		Modifier: 90
		RequiresCondition: storm-level == 2
	RangeMultiplier@IONSTORM3:
		Modifier: 115
		RequiresCondition: storm-level == 3
	FirepowerMultiplier@IONSTORM3:
		Modifier: 120
		RequiresCondition: storm-level == 3
	DamageMultiplier@IONSTORM3:
		Modifier: 85
		RequiresCondition: storm-level == 3
	WithDecoration@IONSTORM1:
		Image: pips
		Sequence: pip-ion1
		Palette: scrin
		Position: BottomLeft
		Margin: -6, 3
		RequiresCondition: storm-level == 1
	WithDecoration@IONSTORM2:
		Image: pips
		Sequence: pip-ion2
		Palette: scrin
		Position: BottomLeft
		Margin: -6, 3
		RequiresCondition: storm-level == 2
	WithDecoration@IONSTORM3:
		Image: pips
		Sequence: pip-ion3
		Palette: scrin
		Position: BottomLeft
		Margin: -6, 3
		RequiresCondition: storm-level == 3

^Hypercharge:
	GrantConditionOnPrerequisite@HYPERCHARGE:
		Condition: hyper-upgrade
		Prerequisites: hyper.upgrade
	GrantTimedConditionOnDeploy@HYPERCHARGE:
		DeployedTicks: 100
		CooldownTicks: 500
		DeployedCondition: hypercharged
		ChargingCondition: hyperchargedebuff
		ShowSelectionBar: true
		ShowSelectionBarWhenFull: false
		StartsFullyCharged: true
		PauseOnCondition: being-warped || empdisable
		DischargingColor: b396f3
		ChargingColor: 8450f8
		DeploySound: hypercharge.aud
		RequiresCondition: hyper-upgrade
		DischargeOnAttack: true
		MaxTicksBeforeDischarge: 375
		Instant: true
	WithPalettedOverlay@HYPERCHARGE:
		RequiresCondition: hypercharged && !(empdisable || invulnerability || invisibility)
		Palette: hypercharge
	WithIdleOverlay@HYPERCHARGE:
		Sequence: emp-overlay
		Palette: ionsurgeoverlay
		RequiresCondition: hypercharged
		IsDecoration: True
	WithPalettedOverlay@HYPERCHARGEDEBUFF:
		Palette: darkened
		RequiresCondition: hyperchargedebuff
	SpeedMultiplier@HYPERCHARGEDEBUFF:
		Modifier: 75
		RequiresCondition: hyperchargedebuff
	FirepowerMultiplier@HYPERCHARGEDEBUFF:
		Modifier: 80
		RequiresCondition: hyperchargedebuff
	ReloadDelayMultiplier@HYPERCHARGEDEBUFF:
		Modifier: 150
		RequiresCondition: hyperchargedebuff
	FirepowerMultiplier@HyperAnathema:
		Modifier: 35
		RequiresCondition: hypercharged && anathema > 1

^Anathema:
	GrantStackingCondition@Anathema:
		Condition: anathema
		DelayPerInstance: 75
		MaximumInstances: 4
		RevokeDelay: -1
		RequiresCondition: anathema
	ExternalCondition@Anathema:
		Condition: anathema
	WithIdleOverlay@Anathema:
		Sequence: idle
		Image: anathema
		Palette: caneon
		RequiresCondition: anathema
		IsDecoration: True
	ReloadDelayMultiplier@Anathema1:
		Modifier: 80
		RequiresCondition: anathema == 1
	FirepowerMultiplier@Anathema1:
		Modifier: 130
		RequiresCondition: anathema == 1
	ReloadDelayMultiplier@Anathema2:
		Modifier: 65
		RequiresCondition: anathema == 2
	FirepowerMultiplier@Anathema2:
		Modifier: 160
		RequiresCondition: anathema == 2
	ReloadDelayMultiplier@Anathema3:
		Modifier: 50
		RequiresCondition: anathema == 3
	FirepowerMultiplier@Anathema3:
		Modifier: 190
		RequiresCondition: anathema == 3
	ReloadDelayMultiplier@Anathema4:
		Modifier: 35
		RequiresCondition: anathema >= 4
	FirepowerMultiplier@Anathema4:
		Modifier: 220
		RequiresCondition: anathema >= 4
	DamageMultiplier@Anathema1:
		Modifier: 75
		RequiresCondition: anathema && anathema < 4
	DamageMultiplier@Anathema2:
		Modifier: 50
		RequiresCondition: anathema >= 4
	GrantTimedCondition@Anathema:
		Condition: anathema-alive
		Duration: 750
		RequiresCondition: anathema
	TimedConditionBar@Anathema:
		Color: 9133ff
		Condition: anathema-alive
	Targetable@Anathema:
		TargetTypes: AnathemaTargetable
	KillsSelf@Anathema:
		RequiresCondition: anathema && !anathema-alive
	FireWarheadsOnDeath@Anathema:
		Weapon: KirovExplode
		EmptyWeapon: KirovExplode
		RequiresCondition: anathema && !anathema-alive
	WithPalettedOverlay@Anathema:
		RequiresCondition: anathema
		Palette: anathema

^ScrinVehicleVoice:
	Voiced:
		VoiceSet: ScrinVehicleVoice

^ScrinUnitPalette:
	RenderSprites:
		PlayerPalette: playerscrin

#
# ---- Infantry
#

^ScrinInfantry:
	Inherits: ^Soldier
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Voiced:
		VoiceSet: ScrinInfantryVoice
	WithInfantryBody:
		IdleSequences: idle1, idle2, idle3, idle4
	WithDeathAnimation:
		DeathSequencePalette: playerscrin
		CrushedSequencePalette: playerscrin
	-GrantConditionOnTerrain@ONTIB:
	-GrantStackingCondition@TIBEXPOSED:
	-DamagedByTerrain@TIBDAMAGE:
	-GrantConditionOnPrerequisite@BIO:
	-GrantConditionOnPrerequisite@HAZMAT:
	-GrantConditionOnPrerequisite@HAZMATSOVIET:
	-GrantConditionOnPrerequisite@HAZMATZOCOM:
	-GrantCondition@HAZMAT:
	-GrantCondition@HAZMATSOVIET:
	-WithDecoration@HAZMAT:
	-WithDecoration@HAZMATSOVIET:
	-GrantConditionOnTerrain@HAZMATSOVIET:
	-SpeedMultiplier@HAZMATSOVIET:
	Targetable@HAZMAT:
		-RequiresCondition:
	WithDecoration@BINO:
		-BlinkPatterns:
	TakeCover:
		ProneSequencePrefix:
		ProneOffset: 0,0,0
	GrantConditionOnPrerequisite@ADVART:
		Condition: advart-upgrade
		Prerequisites: advart.upgrade
	SpeedMultiplier@ADVART:
		RequiresCondition: advart-upgrade
		Modifier: 115
	-DamagedByTintedCells@RADSTRONGHAZMAT:
	-DamagedByTintedCells@RADSTRONGHAZMATSOVIET:
	-DamagedByTintedCells@RADMEDHAZMAT:
	-DamagedByTintedCells@RADWEAKHAZMAT:
	DamagedByTintedCells@RADSTRONG:
		Damage: 375
		RequiresCondition: !being-warped
	DamagedByTintedCells@RADMED:
		Damage: 150
		RequiresCondition: !being-warped
	DamagedByTintedCells@RADWEAK:
		Damage: 50
		RequiresCondition: !being-warped
	DamageTypeDamageMultiplier@FLAKARMOR:
		RequiresCondition: carapace-upgrade
	DamageTypeDamageMultiplier@FLAKARMORMINOR:
		RequiresCondition: carapace-upgrade
	-GrantConditionOnPrerequisite@FLAKARMOR:
	GrantConditionOnPrerequisite@CARAPACE:
		Condition: carapace-upgrade
		Prerequisites: carapace.upgrade

S1:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@BOTHELPER: ^BotCaptureHelper
	Inherits@HeroOfTheUnionTargetable: ^HeroOfTheUnionTargetable
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 12
		Prerequisites: ~infantry.scrin
		Description: General-purpose infantry.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Infantry
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 100
	Tooltip:
		Name: Warrior
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 5000
	Armament@PRIMARY:
		Weapon: WarriorGun
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		LocalOffset: 150,0,400
	Armament@BATF:
		Name: batf
		Weapon: WarriorGunBATF
		MuzzleSequence: garrison-muzzle
		MuzzlePalette: scrin
	WithMuzzleOverlay:
	AttackFrontal:
		PauseOnCondition: being-warped || blinded || reapersnare
		FacingTolerance: 0
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded

S2:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 120
		Prerequisites: anyradar, ~infantry.scrin, ~!evis.upgrade, ~!impl.upgrade, ~!stlk.upgrade, ~techlevel.medium
		IconPalette: chromes
		Description: Fast moving anti-personnel infantry.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 275
	Tooltip:
		Name: Ravager
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 6000
	Mobile:
		Speed: 92
	Armament@PRIMARY:
		Weapon: RavagerShards
		LocalOffset: 0,-100,250, 0,100,250
	Armament@BATF:
		Name: batf
		Weapon: RavagerShards
	AttackFrontal:
		PauseOnCondition: being-warped || blinded || reapersnare
		FacingTolerance: 0
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded
	WithInfantryBody:
		DefaultAttackSequence: shoot
	ReplacedInQueue:
		Actors: evis, impl, stlk

S3:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeAir
	Inherits@BERSERK: ^Berserk
	Inherits@HeroOfTheUnionTargetable: ^HeroOfTheUnionTargetable
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 22
		Prerequisites: ~infantry.scrin
		Description: Anti-tank/anti-aircraft infantry.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Aircraft, Buildings, Defenses
		Weaknesses: • Weak vs Infantry, Light Armor
	Valued:
		Cost: 300
	Tooltip:
		Name: Disintegrator
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 3500
	Mobile:
		Speed: 41
	Armament@PRIMARY:
		Weapon: DisintegratorBeamAA
		LocalOffset: 150,0,400
		PauseOnCondition: !ammo
	Armament@SECONDARY:
		Name: secondary
		Weapon: DisintegratorBeam
		LocalOffset: 150,0,400
		PauseOnCondition: !ammo
	Armament@BATF:
		Name: batf
		Weapon: DisintegratorBeamBATF
	AttackFrontal:
		PauseOnCondition: being-warped || blinded || reapersnare
		FacingTolerance: 0
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded
	AutoTarget:
		ScanRadius: 6
		MaximumScanTimeInterval: 5
	AmmoPool:
		Ammo: 1
		AmmoCondition: ammo
	ReloadAmmoPool:
		Delay: 30
		Count: 1
	WithInfantryBody:
		DefaultAttackSequence: shoot

S4:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 124
		Prerequisites: anyradar, ~infantry.intruder, ~techlevel.medium
		IconPalette: chrome
		Description: Heavy assault infantry with high burst damage.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry\n• Cannot attack Aircraft
		Attributes: • Special Ability: Teleport (with upgrade)
	Mobile:
		Speed: 54
	Valued:
		Cost: 500
	Tooltip:
		Name: Intruder
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 9000
	RevealsShroud:
		Range: 5c0
	Armament@PRIMARY:
		Weapon: IntruderDiscs
		LocalOffset: 150,0,400
	Armament@BATF:
		Name: batf
		Weapon: IntruderDiscsBATF
	AttackFrontal:
		PauseOnCondition: being-warped || blinded || reapersnare
		FacingTolerance: 0
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded
	PortableChronoCA:
		ChargeDelay: 500
		MaxDistance: 16
		ChronoshiftSound: scrinport.aud
		RequiresCondition: blink-upgrade
		TeleportCondition: blink
		ConditionDuration: 3
		ShowSelectionBarWhenFull: false
		CircleColor: c8b8ed66
	GrantConditionOnPrerequisite@BLINKPACKS:
		Condition: blink-upgrade
		Prerequisites: blink.upgrade
	WithColoredOverlay@BLINKFLASH:
		Color: ffffff80
		RequiresCondition: blink
	WithInfantryBody:
		DefaultAttackSequence: shoot

MRDR:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 125
		Prerequisites: anyradar, ~infantry.harbinger, ~techlevel.medium
		IconPalette: chromes
		Description: Heavy assault infantry with high burst damage.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry\n• Cannot attack Aircraft
		Attributes: • Damage against vehicles scales with the max health of the target\n• Special Ability: Teleport (with upgrade)
	Mobile:
		Speed: 46
	Valued:
		Cost: 600
	Tooltip:
		Name: Marauder
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 14000
	RevealsShroud:
		Range: 5c0
	Armament@PRIMARY:
		Weapon: MarauderDiscs
		LocalOffset: 150,0,400
	Armament@BATF:
		Name: batf
		Weapon: IntruderDiscsBATF
	AttackFrontal:
		PauseOnCondition: being-warped || blinded || reapersnare
		FacingTolerance: 0
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded
	PortableChronoCA:
		ChargeDelay: 500
		MaxDistance: 16
		ChronoshiftSound: scrinport.aud
		RequiresCondition: blink-upgrade
		TeleportCondition: blink
		ConditionDuration: 3
		ShowSelectionBarWhenFull: false
		CircleColor: c8b8ed66
	GrantConditionOnPrerequisite@BLINKPACKS:
		Condition: blink-upgrade
		Prerequisites: blink.upgrade
	WithColoredOverlay@BLINKFLASH:
		Color: ffffff80
		RequiresCondition: blink
	WithInfantryBody:
		DefaultAttackSequence: shoot

S6:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@ENGINEER: ^EngineerBase
	Buildable:
		BuildPaletteOrder: 32
		Prerequisites: ~infantry.scrin
		IconPalette: chrome
	Tooltip:
		Name: Assimilator
	Voiced:
		VoiceSet: AssimilatorVoice

WCHR:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 126
		Prerequisites: anyradar, ~infantry.scrin, ~techlevel.medium
		Description: Infiltration unit used to gain vision of enemies.
	Valued:
		Cost: 500
	Tooltip:
		Name: Watcher
	TooltipExtras:
		Weaknesses: • Cannot deal damage
		Attributes: • Can attach parasites to enemy vehicles & structures\n• Cloaked when not moving\n• Special Ability: Teleport (with upgrade)
	Health:
		HP: 5000
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Mobile:
		Speed: 80
	RevealsShroud:
		Range: 6c0
	WithInfantryBody:
		DefaultAttackSequence: shoot
	AttackFrontal:
		FacingTolerance: 0
		PauseOnCondition: being-warped || blinded || reapersnare
	Armament@PRIMARY:
		Weapon: WatcherDart
		LocalOffset: 150,0,400
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: ReturnFire
	Cloak@NORMAL:
		InitialDelay: 250
		CloakDelay: 120
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Move
		CloakedCondition: hidden
		CloakSound: gstealon.aud
		UncloakSound: gstealof.aud
		DetectionTypes: Cloak
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		RequiresCondition: !cloak-force-disabled && !being-warped
		PauseOnCondition: invisibility
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	PortableChronoCA:
		ChargeDelay: 500
		MaxDistance: 16
		ChronoshiftSound: scrinport.aud
		RequiresCondition: blink-upgrade
		TeleportCondition: blink
		ConditionDuration: 3
		ShowSelectionBarWhenFull: false
		CircleColor: c8b8ed66
	GrantConditionOnPrerequisite@BLINKPACKS:
		Condition: blink-upgrade
		Prerequisites: blink.upgrade
	WithColoredOverlay@BLINKFLASH:
		Color: ffffff80
		RequiresCondition: blink

BRST:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 160
		Prerequisites: scrt, ~infantry.scrin, ~techlevel.high
		IconPalette: chromes
		Description: Explodes on death or on contact with target.
	Valued:
		Cost: 550
	Tooltip:
		Name: Burster
	TooltipExtras:
		Strengths: • Strong vs Infantry, Buildings, Light Armor
		Weaknesses: • Cannot attack Aircraft
	Health:
		HP: 5000
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Mobile:
		Speed: 92
	RevealsShroud:
		Range: 7c0
	FireWarheadsOnDeath:
		Weapon: BursterExplode
		EmptyWeapon: BursterExplode
	AttackFrontal:
		FacingTolerance: 64
		PauseOnCondition: being-warped || blinded || reapersnare
	Armament@PRIMARY:
		Weapon: BursterTargeting
	GrantConditionOnAttack:
		Condition: triggered
	KillsSelf:
		RequiresCondition: (triggered || berserk)
	-TakeCover:
	Targetable@Burster:
		TargetTypes: Burster
	AutoTarget:
		ScanRadius: 4
	TargetedAttackAbility:
		TargetCursor: attack
		ArmamentNames: primary
		CircleWidth: 0
		Type: BursterAttack

EVIS:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@RESCONV: ^ResourceConversion
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 121
		Prerequisites: anyradar, ~infantry.scrin, ~evis.upgrade, ~techlevel.medium
		IconPalette: chromes
		Description: Fast moving anti-personnel infantry.
	Selectable:
		DecorationBounds: 640, 896, -32, -256
	Valued:
		Cost: 600
	Tooltip:
		Name: Eviscerator
	TooltipExtras:
		Strengths: • Strong vs Light Armor, Infantry
		Weaknesses: • Weak vs Heavy Armor, Defenses\n• Cannot attack Aircraft
	Health:
		HP: 12000
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Mobile:
		Speed: 92
	RevealsShroud:
		Range: 6c0
	AttackFrontal:
		FacingTolerance: 64
		PauseOnCondition: being-warped || blinded || reapersnare
		Armaments: primary, primary-charged
	Armament@PRIMARY:
		Weapon: EvisceratorShards
		LocalOffset: 0,-100,250, 0,100,250
		PauseOnCondition: (ammo || charged-fire) && !uncharged-fire
	Armament@PRIMARYCHARGED:
		Name: primary-charged
		Weapon: EvisceratorShardsCharged
		PauseOnCondition: !ammo || uncharged-fire
		LocalOffset: 0,-100,250, 0,100,250
	Armament@BATF:
		Name: batf
		Weapon: EvisceratorShards
	AmmoPool@RESCONV:
		Ammo: 20
	ReloadAmmoPool@RESCONV:
		Count: 5
	WithAmmoPipsDecoration@RESCONV:
		PipCount: 4
	GrantCondition@RESCONVCHARGE:
		RequiresCondition: resconv-upgrade && on-resource && (ammo < 20 || charged-fire)
	GrantConditionOnAttack@PRIMARY:
		RevokeDelay: 28
	GrantConditionOnAttack@PRIMARYCHARGED:
		RevokeDelay: 28
	WithIdleOverlay@RESCONVTIB:
		Offset: 0,0,150
		Sequence: greensm
	WithIdleOverlay@RESCONVORE:
		Offset: 0,0,150
		Sequence: yellowsm
	WithIdleOverlay@RESCONVBLUETIB:
		Offset: 0,0,150
		Sequence: bluesm
	WithIdleOverlay@RESCONVGEMS:
		Offset: 0,0,150
		Sequence: redsm
	WithInfantryBody:
		DefaultAttackSequence: shoot

IMPL:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 122
		Prerequisites: anyradar, ~infantry.scrin, ~impl.upgrade, ~techlevel.medium
		IconPalette: chromes
		Description: Assault infantry with high burst damage.\n  Impales targets, slowing their movement.
	Selectable:
		DecorationBounds: 640, 896, -32, -256
	Valued:
		Cost: 600
	Tooltip:
		Name: Impaler
	TooltipExtras:
		Strengths: • Strong vs Light Armor, Infantry
		Weaknesses: • Weak vs Heavy Armor, Defenses\n• Cannot attack Aircraft
		Attributes: • Slows targets
	Health:
		HP: 9000
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Mobile:
		Speed: 60
	RevealsShroud:
		Range: 6c0
	AttackFrontal:
		FacingTolerance: 64
		PauseOnCondition: being-warped || blinded
	Armament@PRIMARY:
		Weapon: ImpalerSpike
		LocalOffset: 150,0,400
	Armament@BATF:
		Name: batf
		Weapon: ImpalerSpike
	WithInfantryBody:
		DefaultAttackSequence: shoot

STLK:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 123
		Prerequisites: anyradar, ~infantry.scrin, ~stlk.upgrade, ~techlevel.medium
		IconPalette: chromes
		Description: Fast moving anti-personnel infantry.
	Selectable:
		DecorationBounds: 640, 896, -32, -256
	Valued:
		Cost: 500
	Tooltip:
		Name: Stalker
	TooltipExtras:
		Strengths: • Strong vs Light Armor, Infantry
		Weaknesses: • Weak vs Heavy Armor, Defenses\n• Cannot attack Aircraft
		Attributes: • Special Ability: Cloak\n• Moves faster while cloaked.
	Health:
		HP: 12000
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Mobile:
		Speed: 92
	RevealsShroud:
		Range: 6c0
	AttackFrontal:
		FacingTolerance: 64
		PauseOnCondition: being-warped || blinded || reapersnare
	Armament@PRIMARY:
		Weapon: StalkerShards
		LocalOffset: 0,-100,250, 0,100,250
	Armament@BATF:
		Name: batf
		Weapon: StalkerShards
	AutoTarget:
		ScanRadius: 5
	WithInfantryBody:
		DefaultAttackSequence: shoot
	GrantTimedConditionOnDeploy@Cloak:
		DeployedCondition: cloak
		ShowSelectionBar: true
		StartsFullyCharged: true
		DeployedTicks: 250
		CooldownTicks: 500
		ShowSelectionBarWhenFull: false
		ChargingColor: 808080
		DischargingColor: ffffff
		Instant: true
	Cloak@NORMAL:
		InitialDelay: 0
		CloakDelay: 50
		CloakSound: stlk-cloak.aud
		UncloakSound: stlk-uncloak.aud
		CloakedCondition: hidden
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Damage, Heal
		RequiresCondition: !cloak-force-disabled && !being-warped && !parachute && cloak
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden)
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	SpeedMultiplier@CloakSpeed:
		Modifier: 150
		RequiresCondition: hidden
	GrantTimedCondition@Uncloaked:
		Condition: phaseshield
		RequiresCondition: !hidden
		Duration: 25
	DamageMultiplier@UncloakShield:
		Modifier: 33
		RequiresCondition: phaseshield
	WithColoredOverlay@UncloakShield:
		Color: 15091f77
		RequiresCondition: phaseshield

SMEDI:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 240
		Prerequisites: ~disabled, ~infantry.scrin
		IconPalette: chromes
		Description: Heals nearby life-forms.
	TooltipExtras:
		Weaknesses: • Unarmed
	Valued:
		Cost: 200
	Tooltip:
		Name: Rejuvenator
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 5000
	Armament:
		Weapon: Heal
		Cursor: heal
		OutsideRangeCursor: heal
		TargetRelationships: Ally
		ForceTargetRelationships: None
	AttackFrontal:
		PauseOnCondition: being-warped || blinded || reapersnare
		FacingTolerance: 0
	AttackSoundsCA@HEALSOUND:
		Sounds: heal2.aud
	WithInfantryBody:
		DefaultAttackSequence: heal
	Passenger:
		CustomPipType: blue
		Voice: Move
	Guard:
		Voice: Move
	AutoTarget:
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry
		ValidRelationships: Ally
	Voiced:
		VoiceSet: AssimilatorVoice
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	GuardsSelection:
		ValidTargets: Infantry
	KeepsDistance:

ARTI:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 240
		Prerequisites: ~disabled, ~infantry.scrin
		IconPalette: chromes
		Description: Repairs nearby vehicles.
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Can salvage destroyed vehicles for credits
	Valued:
		Cost: 400
	Tooltip:
		Name: Artificer
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 5000
	Armament:
		Weapon: ScrinRepair
		Cursor: repair
		OutsideRangeCursor: repair
		TargetRelationships: Ally
		ForceTargetRelationships: None
	Armament@defuse:
		Weapon: DefuseKit
		Cursor: goldwrench
		OutsideRangeCursor: goldwrench
		TargetRelationships: Ally
		ForceTargetRelationships: None
		Name: secondary
	AutoTargetPriority@defuse:
		ValidTargets: C4Attached
		InvalidTargets: NoAutoTarget
		ValidRelationships: Ally
	AttackFrontal:
		PauseOnCondition: being-warped || blinded || reapersnare
		FacingTolerance: 0
	AttackSoundsCA@REPAIRSOUND:
		Sounds: floatk1.aud
	WithInfantryBody:
		IdleSequences: idle1, idle2, idle3, idle4
		DefaultAttackSequence: repair
		StandSequences: stand, stand2
		RequiresCondition: !parachute && !being-warped && !salvaging
	WithInfantryBody@SALVAGE:
		StandSequences: repair
		RequiresCondition: salvaging
	CaptureManager:
		CapturingCondition: salvaging
	Captures:
		CaptureTypes: husk
		PlayerExperience: 5
		ConsumedByCapture: False
		CaptureDelay: 75
		PlayerExperienceRelationships: Neutral, Enemy
		EnterCursor: sell2
		EnterBlockedCursor: move-blocked
	Passenger:
		CustomPipType: blue
		Voice: Move
	Guard:
		Voice: Move
	AutoTarget:
	AutoTargetPriority@DEFAULT:
		ValidTargets: Repair
		ValidRelationships: Ally
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded
	Voiced:
		VoiceSet: AssimilatorVoice
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	GuardsSelection:
		ValidTargets: Vehicle
	KeepsDistance:

GSCR:
	Inherits: BRUT
	RenderSprites:
		Image: gscr
		PlayerPalette: playerscrin
	Voiced:
		VoiceSet: GiantScrinVoice
	WithDeathAnimation:
		DeathSequencePalette: playerscrin
	-Buildable:
	Tooltip:
		Name: Brutalizer
	-GrantConditionOnTerrain@ONTIB:
	-GrantStackingCondition@TIBEXPOSED:
	-DamagedByTerrain@TIBDAMAGE:
	WithInfantryBody:
		MoveSequence: run
		StandSequences: stand
		DefaultAttackSequence: bash
		IdleSequences: idle1, idle2, idle3, idle4
	AttackSounds:
		Sounds: gscr-attack1.aud, gscr-attack2.aud
	Selectable:
		DecorationBounds: 640, 896, -32, -256

GSCR.Mutating:
	Inherits: BRUT.Mutating
	SpawnActorOnDeath@GMUT:
		Actor: gscr
	RenderSprites:
		Image: gscr
		PlayerPalette: playerscrin
	WithDeathAnimation:
		DeathSequencePalette: playerscrin
	Tooltip:
		Name: Brutalizer

MAST:
	Inherits@SCRININFANTRY: ^ScrinInfantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Inherits@CommandoRegen: ^CommandoRegen
	Inherits@HEALINGCOOLDOWN: ^HealingCooldown
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !maxcontrolled || stance-attackanything || assault-move
	Buildable:
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 220
		Prerequisites: scrt, ~infantry.scrin, ~techlevel.high
		BuildLimit: 1
		IconPalette: chrome
		Description: Elite specialist infantry able to mind-control\n  enemy units (up to 3, increases with veterancy).
	TooltipExtras:
		Strengths: • Strong vs Infantry, Vehicles, Defenses, Buildings
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Maximum 1 can be trained\n• Can detect cloaked ground units and spies\n• Immune to mind control\n• Can capture enemy buildings\n• Shorter range and longer reload against vehicles\n• Special Ability: Mind Spark
	Voiced:
		VoiceSet: MastermindVoice
	Valued:
		Cost: 1350
	Tooltip:
		Name: Mastermind
		RequiresCondition: !rank-elite
	Tooltip@PRODIGY:
		Name: Prodigy
		RequiresCondition: rank-elite
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 18000
	Mobile:
		Speed: 60
	RevealsShroud:
		Range: 7c0
	IgnoresDisguise:
	DetectCloaked:
		DetectionTypes: Cloak, Thief
		Range: 5c0
	Armament@PRIMARY:
		Weapon: EnslaveInfantry
		LocalOffset: 150,0,400
	Armament@SECONDARY:
		Name: secondary
		Weapon: EnslaveVehicle
		LocalOffset: 150,0,400
		PauseOnCondition: !vs-vehicle-ammo
	Armament@BATF:
		Name: batf
		Weapon: PsychicBeamBATF
	MindController:
		ControlType: MindControl
		ArmamentNames: primary, secondary
		Capacity: 3
		ControlSounds: mastermind-fire.aud
		ReleaseSounds: mastermind-release.aud
		ControllingCondition: mindcontrolling
		MaxControlledCondition: maxcontrolled
		ControlAtCapacityBehaviour: DetonateOldest
		SlaveDeployEffect: Detonate
		SlaveDetonateWeapon: DetonateSlave
		SlaveKillDamageTypes: BulletDeath
		ExperienceFromControl: 100
		TargetTypeTicksToControl:
			MindControlResistant: 105
	WithMindControlArc@MC:
		ControlType: MindControl
		Color: c71585
		Transparency: 55
		Offset: 0,0,511
		Angle: 32
		Width: 116
	AttackFrontal:
		PauseOnCondition: being-warped || blinded || reapersnare || parachute || !ammo
		FacingTolerance: 0
	Selectable:
		DecorationBounds: 640, 896, -32, -256
	WithInfantryBody:
		DefaultAttackSequence: shoot
		RequiresCondition: !rank-elite && !being-warped
	WithInfantryBody@Warped:
		RequiresCondition: !rank-elite && being-warped
	WithDeathAnimation:
		RequiresCondition: !rank-elite && !being-warped
	WithInfantryBody@Prodigy:
		StandSequences: pdgy-stand
		MoveSequence: pdgy-run
		IdleSequences: pdgy-idle1, pdgy-idle2, pdgy-idle3, pdgy-idle4
		DefaultAttackSequence: pdgy-shoot
		RequiresCondition: rank-elite && !transforming && !being-warped
	WithInfantryBody@ProdigyWarped:
		StandSequences: pdgy-stand
		RequiresCondition: rank-elite && !transforming && being-warped
	WithDeathAnimation@PDGY:
		DeathSequencePalette: playerscrin
		CrushedSequencePalette: scrin
		DeathSequence: pdgy-die
		DeathTypes:
			DefaultDeath: 1
			BulletDeath: 2
			SmallExplosionDeath: 3
			ExplosionDeath: 4
			FireDeath: 5
			ElectricityDeath: 6
			PoisonDeath: 7
			ChronoDeath: 8
			ToxinDeath: 9
			RadiationDeath: 10
			FrozenDeath: 11
			AtomizedDeath: 12
		CrushedSequence: die-crushed
		RequiresCondition: rank-elite && !transforming && !being-warped
	WithSpriteBody@PRODIGYMAKE:
		RequiresCondition: transforming
		Sequence: stand
	GrantTimedCondition@PRODIGYMAKE:
		Condition: transforming
		Duration: 25
		RequiresCondition: rank-elite
	WithEnabledAnimation@PRODIGYMAKE:
		RequiresCondition: transforming
		Sequence: pdgy-make
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded
	-Crushable:
	TakeCover:
		SpeedModifier: 70
	ExternalCondition@PRODUCED:
		Condition: produced
	VoiceAnnouncement:
		RequiresCondition: produced
		Voice: Build
	CaptureManager:
	Captures:
		CaptureTypes: building
		CaptureDelay: 150
		ConsumedByCapture: false
		EnterCursor: mc-capture
	Targetable:
		TargetTypes: Ground, Infantry
	Targetable@Hero:
		TargetTypes: Hero
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@ReaperSnareImmune:
		TargetTypes: ReaperSnareImmune
	WithIdleOverlay@mindcontrolling:
		Sequence: mc
		Palette: scrin
		RequiresCondition: mindcontrolling
		Offset: 0,0,400
	Passenger:
		CustomPipType: red
	AutoTargetPriority@VEHCOOLDOWN:
		ValidTargets: Infantry
		InvalidTargets: NoAutoTarget
		Priority: 10
		RequiresCondition: !vs-vehicle-ammo
	AmmoPool:
		Ammo: 1
		AmmoCondition: ammo
	AmmoPool@Vehicles:
		Name: secondary
		Armaments: secondary
		Ammo: 1
		AmmoCondition: vs-vehicle-ammo
	ReloadAmmoPool:
		Delay: 35 # matches EnslaveInfantry ReloadDelay
		Count: 1
	ReloadAmmoPool@Vehicles:
		AmmoPool: secondary
		Delay: 100
		Count: 1
	MindControllerCapacityModifier@RANK-1:
		Amount: 1
		RequiresCondition: rank-veteran == 1
	MindControllerCapacityModifier@RANK-2:
		Amount: 2
		RequiresCondition: rank-veteran == 2
	MindControllerCapacityModifier@RANK-ELITE:
		Amount: 3
		RequiresCondition: rank-elite
	RangeMultiplier@RANK-ELITE:
		Modifier: 115
		RequiresCondition: rank-elite
	ChangesHealth@ELITE:
		Delay: 50
	SpawnActorAbility:
		Actors: mspk
		SpawnSounds: mastermind-shatter.aud
		TargetModifiedCursor: ability2
		Behavior: SpawnAtSelfAndSlavesAndMoveToTarget
		RequiresCondition: !being-warped && mspk-ammo
		AmmoPool: mspk
	AmmoPool@MindSpark:
		Name: mspk
		Ammo: 1
		Armaments: none
		AmmoCondition: mspk-ammo
	ReloadAmmoPoolCA@MindSpark:
		AmmoPool: mspk
		Delay: 375
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: ff00ff

MAST.Clone:
	Inherits: MAST
	Buildable:
		Prerequisites: ~disabled
	RenderSprites:
		Image: mast
	Tooltip:
		Name: Mastermind Clone
	Tooltip@PRODIGY:
		Name: Prodigy Clone
	Valued:
		Cost: 1000
	Health:
		HP: 14000
	RangeMultiplier@Clone:
		Modifier: 80

#
# ---- Vehicles
#

HARV.Scrin:
	Inherits: HARV.TD
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Selectable:
		DecorationBounds: 1792, 1792
	RenderSprites:
		Image: harv.scrin
		PlayerPalette: player
	WithShadow:
		Offset: 43, 255, 0
		ZOffset: -256
		RequiresCondition: !invisibility
	WithHarvestOverlay:
		Palette: scrin
		LocalOffset: 400, 0, 0
	Buildable:
		BuildPaletteOrder: 13
		Prerequisites: ~proc.scrin, ~vehicles.scrin, ~techlevel.low
		IconPalette: chrome
	SpawnActorOnDeath:
		Actor: HARV.Scrin.Husk
	-WithDockingAnimationCA:
	-WithHarvestAnimation:
	WithDamageOverlay:
		Palette: chrometd
	-SpawnRandomActorOnDeath:
	-GrantConditionOnPrerequisite@Stealth:
	-TransformOnCondition:
	-RejectsOrders@Transforming:
	-TransferResourcesOnTransform:
	-ReplacedInQueue:

GUNW:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeAir
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 60
		Prerequisites: ~vehicles.scrin, ~!shrw.upgrade, ~techlevel.low
		Description: Durable light assault walker.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Aircraft
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses
	Valued:
		Cost: 600
	Tooltip:
		Name: Gun Walker
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 28000
	Armor:
		Type: Light
	Mobile:
		Speed: 100
		TurnSpeed: 38
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: GunWalkerZap
		LocalOffset: 690,92,0, 690,-92,0
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	Armament@PRIMARYVISUAL:
		Weapon: GunWalkerZapVisual
		LocalOffset: 690,92,0, 690,-92,0
	Armament@SECONDARY:
		Name: secondary
		Weapon: GunWalkerZapAA
		LocalOffset: 690,92,0, 690,-92,0
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	Armament@SECONDARYVISUAL:
		Name: secondary
		Weapon: GunWalkerZapAAVisual
		LocalOffset: 690,92,0, 690,-92,0
	WithMuzzleOverlay:
	AttackFrontal:
		FacingTolerance: 40
		PauseOnCondition: empdisable || being-warped || blinded
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	WithMoveAnimation:
		ValidMovementTypes: Horizontal, Vertical, Turn
	GrantConditionOnFaction@TRAVELERSPEED:
		Condition: travelerspeed
		Factions: traveler
	SpeedMultiplier@TRAVELERSPEED:
		RequiresCondition: travelerspeed
		Modifier: 115
	ReplacedInQueue:
		Actors: shrw
	Upgradeable@SHRW:
		Type: shrw.upgrade
		Actor: shrw
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100

SHRW:
	Inherits: GUNW
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Shard Walker
	Buildable:
		Prerequisites: ~vehicles.scrin, ~shrw.upgrade, ~techlevel.medium
		IconPalette: chromes
	Armor:
		Type: Light
	Health:
		HP: 36000
	Mobile:
		Speed: 92
		TurnSpeed: 36
	Armament@PRIMARY:
		Weapon: ShardWalkerShards
		LocalOffset: 690,92,0, 690,-92,0
	-Armament@PRIMARYVISUAL:
	Armament@SECONDARY:
		Weapon: ShardWalkerShardsAA
		LocalOffset: 690,92,0, 690,-92,0
	-GrantConditionOnFaction@TRAVELERSPEED:
	-SpeedMultiplier@TRAVELERSPEED:
	-ReplacedInQueue:
	-Upgradeable@SHRW:

SEEK:
	Inherits: ^Vehicle
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Inherits@Hypercharge: ^Hypercharge
	Valued:
		Cost: 750
	Tooltip:
		Name: Seeker
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 160
		Prerequisites: ~vehicles.seek, ~techlevel.low
		Queue: VehicleSQ, VehicleMQ
		Description: Light hover tank.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Buildings
		Weaknesses: • Weak vs Infantry\n• Cannot attack Aircraft
		Attributes: • Can detect cloaked units, spies and submarines.\n• Can traverse water
	Mobile:
		Speed: 113
		Locomotor: lighthover
	Health:
		HP: 23000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted:
		TurnSpeed: 40
		Offset: -280,0,0
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	Armament@PRIMARY:
		Weapon: PlasmaDiscs
		LocalOffset: 320,-25,320, 320,25,320
		RequiresCondition: !hypercharged
	Armament@PRIMARYCHARGED:
		Weapon: PlasmaDiscs.Hypercharged
		LocalOffset: 320,-25,320, 320,25,320
		RequiresCondition: hypercharged
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithSpriteTurret:
	IgnoresDisguise:
	DetectCloaked:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak, Underwater
		RequiresCondition: !(empdisable || being-warped)
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater

LACE:
	Inherits: ^Vehicle
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Inherits@Hypercharge: ^Hypercharge
	Valued:
		Cost: 650
	Tooltip:
		Name: Lacerator
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 161
		Prerequisites: ~vehicles.traveler, ~techlevel.low
		Queue: VehicleSQ, VehicleMQ
		Description: Fast hover tank.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Buildings
		Weaknesses: • Weak vs Infantry\n• Cannot attack Aircraft
		Attributes: • Can detect cloaked units, spies and submarines.\n• Can traverse water
	Mobile:
		TurnSpeed: 40
		Speed: 144
		Locomotor: lighthover
	Health:
		HP: 13000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted:
		TurnSpeed: 40
		Offset: -50,0,0
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	GrantConditionOnTerrain:
		TerrainTypes: Water
		Condition: onwater
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	Armament@PRIMARY:
		Weapon: LaceratorShards
		LocalOffset: 0,70,320, 0,-70,320
		RequiresCondition: !hypercharged
	Armament@PRIMARYCHARGED:
		Weapon: LaceratorShards.Hypercharged
		LocalOffset: 0,70,320, 0,-70,320
		RequiresCondition: hypercharged
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithSpriteTurret:
	IgnoresDisguise:
	DetectCloaked:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak, Underwater
		RequiresCondition: !(empdisable || being-warped)
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater

INTL:
	Inherits: ^Vehicle
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Inherits@SHIELDS: ^ScrinShields
	Valued:
		Cost: 800
	Tooltip:
		Name: Interloper
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 110
		Prerequisites: ~vehicles.scrin, ~techlevel.low
		Queue: VehicleSQ, VehicleMQ
		Description: Heavily armored front-line assault vehicle and troop carrier.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
		Attributes: • Can traverse water
	Mobile:
		Speed: 60
		TurnSpeed: 512
		Locomotor: lighthover
	Health:
		HP: 44000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: InterloperLaser
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		LocalOffset: 470,84,30, 470,-84,30
	WithMuzzleOverlay:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	Cargo:
		Types: Infantry, Hacker
		MaxWeight: 5
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			e6: loaded-engi
			n6: loaded-engi
			s6: loaded-engi
			e7: loaded-cmdo
			rmbo: loaded-cmdo
			bori: loaded-cmdo
			yuri: loaded-cmdo
			mast: loaded-cmdo
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
	CargoBlocked:
		RequiresCondition: mindcontrolled
	WithCargoPipsDecoration:
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: loaded-cmdo
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	Capturable:
		RequiresCondition: !being-warped && !cargo
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !cargo
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: cargo
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: loaded-cmdo
	TurretedFloating:
		TurnSpeed: 16
		Offset: 0,0,0
		RealignDelay: 75
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Armaments: primary
		Turrets: primary, shield
	WithSpriteTurret:
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater
	DetectCloaked:
		Range: 4c0
		DetectionTypes: Mine
		RequiresCondition: loaded-engi
	-WithFacingSpriteBody@SHIELDS:
	WithSpriteTurret@SHIELDS:
		Turret: shield
		RequiresCondition: shields-upgrade && shields-up
		Sequence: shield
		Palette: scrinshield-ignore-lighting-alpha50
	TurretedFloating@SHIELDS:
		Turret: shield
		TurnSpeed: 16
		RealignDelay: 75
		RequiresCondition: shields-upgrade
	Shielded:
		MaxStrength: 6000

INTL.AI:
	Inherits: INTL
	RenderSprites:
		Image: INTL
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.scrin, ~!scrt, ~techlevel.low
	WithCargoSounds:
		-EnterSounds:

INTL.AI2:
	Inherits: INTL.AI
	Inherits@AIUNLOAD: ^AIUNLOAD
	RenderSprites:
		Image: INTL
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.scrin, ~scrt, ~techlevel.low
	Cargo:
		InitialUnits: S1,S1,S2,S3,S3

CORR:
	Inherits: ^Tank
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Valued:
		Cost: 1100
	Tooltip:
		Name: Corrupter
	Selectable:
		DecorationBounds: 1536, 1536, 0, -128
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 210
		Prerequisites: anyradar, ~vehicles.corr, ~techlevel.medium
		Queue: VehicleSQ, VehicleMQ
		Description: Medium-ranged anti-infantry/structure unit.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Infantry, Buildings, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Defenses\n• Cannot attack Aircraft\n• Has difficulty hitting moving targets
	Mobile:
		Speed: 72
		TurnSpeed: 16
	Health:
		HP: 36000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: CorrupterSpew
		LocalOffset: 750,0,100
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	FireWarheadsOnDeath:
		Weapon: CorrupterExplode
		EmptyWeapon: CorrupterExplode
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	WithMoveAnimation:
		ValidMovementTypes: Horizontal, Vertical, Turn
	GrantConditionOnFaction@TRAVELERSPEED:
		Condition: travelerspeed
		Factions: traveler
	SpeedMultiplier@TRAVELERSPEED:
		RequiresCondition: travelerspeed
		Modifier: 115

LCHR:
	Inherits: ^Tank
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Valued:
		Cost: 1000
	Tooltip:
		Name: Leecher
	Selectable:
		DecorationBounds: 1536, 1536, 0, -128
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 211
		Prerequisites: anyradar, ~vehicles.collector, ~techlevel.medium
		Queue: VehicleSQ, VehicleMQ
		Description: Medium-ranged anti-infantry/structure unit.
		IconPalette: chromes
	TooltipExtras:
		Strengths: • Strong vs Infantry, Buildings, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Defenses\n• Cannot attack Aircraft
		Attributes: • Regenerates health while dealing damage (without upgrade)\n• Disables power plants\n• Regenerates and heals allies when in coalescence form (with upgrade)\n• Special Ability: Coalescence (with upgrade)
	Mobile:
		Speed: 82
		TurnSpeed: 20
	Health:
		HP: 28000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: LeecherBeam
		LocalOffset: 720,0,200, 720,-500,0, 720,500,0
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	WithMuzzleOverlay:
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	WithMoveAnimation:
		ValidMovementTypes: Horizontal, Vertical, Turn
	ConvertsDamageToHealth:
		DamagePercentConverted: 30
		RequiresCondition: !coalescence-upgrade
		InvalidTargetTypes: Husk
	FireWarheadsOnDeath:
		Weapon: LeecherExplode
		EmptyWeapon: LeecherExplode
		RequiresCondition: !coalescence-upgrade && !being-warped
	FireWarheadsOnDeath@COALESCENCE:
		Weapon: LeecherCoalesce
		EmptyWeapon: LeecherCoalesce
		RequiresCondition: coalescence-upgrade && !being-warped
	GrantConditionOnPrerequisite@COALESCENCE:
		Condition: coalescence-upgrade
		Prerequisites: coalescence.upgrade
	SpawnActorOnDeathCA:
		Actor: lchr.orb
		RequiresCondition: coalescence-upgrade && !being-warped
		InheritsSelection: true
		InheritsControlGroup: true
		InheritsStance: true
		InheritsExperience: true
	KillsSelf:
		RequiresCondition: triggered
	ActorLostNotification:
		RequiresCondition: !coalescence-upgrade
	GrantConditionOnDeploy:
		DeployedCondition: triggered
		RequiresCondition: coalescence-upgrade && !being-warped
	GivesBountyCA:
		RequiresCondition: global-bounty && !coalescence-upgrade

LCHR.Orb:
	Inherits@1: ^1x1Shape
	Inherits@2: ^SelectableSupportUnit
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@bounty: ^GlobalBounty
	Inherits@Warpable: ^Warpable
	Selectable:
		DecorationBounds: 1536, 1536, 0, -128
	Tooltip:
		Name: Leecher Orb
	TooltipExtras:
		Description: Heals nearby allies. Will revive into a Leecher if not destroyed.
	RenderSprites:
		Image: lchr.orb
		Palette: caneon
	WithSpriteBody:
		RequiresCondition: !being-warped
	WithSpriteBody@Warped:
		Name: warped
		Sequence: warped
		RequiresCondition: being-warped
	ClassicFacingBodyOrientation:
		QuantizedFacings: 1
	Targetable:
		TargetTypes: Ground, Vehicle
	HiddenUnderFog:
		Type: CenterPosition
	RevealsShroud:
		Range: 4c0
		Type: CenterPosition
	Health:
		HP: 35000
	Armor:
		Type: Light
	Valued:
		Cost: 1000
	Immobile:
		OccupiesSpace: true
	PeriodicExplosion@init:
		Weapon: LeecherOrbHealInit
	PeriodicExplosion@hot:
		Weapon: LeecherOrbHeal
	KillsSelf:
		Delay: 0
		RequiresCondition: resurrect
	FireWarheadsOnDeath:
		Weapon: LeecherExplode
		EmptyWeapon: LeecherExplode
		RequiresCondition: !resurrect
	FireWarheadsOnDeath@COALESCENCE:
		Weapon: LeecherCoalesce
		EmptyWeapon: LeecherCoalesce
		RequiresCondition: resurrect
	ActorLostNotification:
		RequiresCondition: !resurrect
	AmmoPool:
		Ammo: 1
		InitialAmmo: 0
		AmmoCondition: resurrect
	ReloadAmmoPoolCA:
		Delay: 500
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: ff0000
		PauseOnCondition: being-warped
	SpawnActorOnDeathCA:
		Actor: lchr
		RequiresCondition: resurrect
		InheritsSelection: true
		InheritsControlGroup: true
		InheritsStance: true
		InheritsExperience: true
	ProximityExternalCondition@LCHRHEAL:
		Condition: lchr-healing
		Range: 2c256
	AutoTarget:
	AttackOmni:
	WithColoredSelectionBox@PLAYERBOX:
		ColorSource: Player
		ValidRelationships: Ally, Enemy
	WithRangeCircleCA:
		Type: LeecherOrb
		Range: 2c256
		UsePlayerColor: true
		PlayerColorAlpha: 160
		Visible: Always

STCR:
	Inherits: ^Tank
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@IONCONDUITS: ^IonConduits
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Inherits@HULLREGEN: ^HullRegen
	Inherits@SHIELDS: ^ScrinShields
	Valued:
		Cost: 1350
	Tooltip:
		Name: Stormcrawler
	Selectable:
		DecorationBounds: 1536, 1536, 0, -128
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 213
		Prerequisites: anyradar, ~vehicles.reaper, ~techlevel.medium
		Queue: VehicleSQ, VehicleMQ
		Description: Slow moving, very durable, short-range assault unit.
		IconPalette: chromes
	TooltipExtras:
		Strengths: • Strong vs Defenses, Buildings
		Weaknesses: • Weak vs Infantry\n• Cannot attack Aircraft
	Mobile:
		Speed: 46
		TurnSpeed: 16
	Health:
		HP: 84000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: StormcrawlerZap
		LocalOffset: 0,200,350, -250,-200,350, 250,200,350, 0,-200,350, -250,200,350, 250,-200,350
	AttackFollowFrontal:
		FacingTolerance: 512
		PauseOnCondition: empdisable || being-warped || blinded
		RangeMargin: 0
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	WithShadow:
		Offset: 80, 80, 0
		ZOffset: -81
		RequiresCondition: !invisibility
	SpawnActorOnDeath:
		Actor: STCR.Husk
		RequiresCondition: !being-warped
	LaysMinefield@IONSTORM:
		Mines: strm2
		Locations: 0,0
		KillOnRemove: false
		RemoveOnDisable: false
		RecreationInterval: 100
		RequiresCondition: storm-active
	RangeMultiplier@IONSTORM1:
		Modifier: 110
	RangeMultiplier@IONSTORM2:
		Modifier: 120
	RangeMultiplier@IONSTORM3:
		Modifier: 130
	Shielded:
		MaxStrength: 10000

DEVO:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@RESCONV: ^ResourceConversion
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 212
		Prerequisites: anyradar, ~vehicles.scrin, ~techlevel.medium
		Description: Maneuverable medium-range hover tank.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses
		Weaknesses: • Weak vs Buildings, Infantry\n• Cannot attack Aircraft
		Attributes: • Can traverse water
	Valued:
		Cost: 1350
	Tooltip:
		Name: Devourer Tank
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 27500
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 512
		Speed: 70
		Locomotor: lighthover
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: DevourerLaser
		PauseOnCondition: (ammo || charged-fire) && !uncharged-fire
		LocalOffset: 850,0,208
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	Armament@PRIMARYCHARGED:
		Name: primary-charged
		Weapon: DevourerLaserCharged
		PauseOnCondition: !ammo || uncharged-fire
		LocalOffset: 850,0,208
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	WithMuzzleOverlay:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	TurretedFloating:
		TurnSpeed: 16
		Offset: 0,0,0
		RealignDelay: 100
	AttackTurreted:
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		Armaments: primary, primary-charged
	WithSpriteTurret:
	ProductionCostMultiplier@REAPBONUS:
		Multiplier: 90
		Prerequisites: player.reaper
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater
	GrantConditionOnAttack@PRIMARYCHARGED:
		RequiresCondition: !anathema
	GrantConditionOnAttack@PRIMARYCHARGEDANATHEMA:
		ArmamentNames: primary-charged
		Condition: charged-fire
		RevokeDelay: 40
		RequiresCondition: anathema

DARK:
	Inherits: ^Vehicle
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 292
		Prerequisites: scrt, ~vehicles.scrin, ~malefic.allegiance, ~techlevel.high
		Description: Heavy hover tank armed with a rift cannon.
		IconPalette: chromes
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses
		Weaknesses: • Weak vs Buildings, Infantry\n• Cannot attack Aircraft
		Attributes: • Can traverse water\n• Damage scales with the max health of the target
	Valued:
		Cost: 1350
	Tooltip:
		Name: Darkener Tank
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 38000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 16
		Speed: 60
		Locomotor: hover
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: RiftCannon
		LocalOffset: 835,0,350
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	WithMuzzleOverlay:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Turreted:
		TurnSpeed: 16
		Offset: 0,0,0
	AttackTurreted:
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		Armaments: primary
	WithSpriteTurret:
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater

RUIN:
	Inherits: ^Vehicle
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@RESCONV: ^ResourceConversion
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Valued:
		Cost: 1250
	Tooltip:
		Name: Ruiner
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 290
		Prerequisites: scrt, ~vehicles.scrin, ~loyalist.allegiance, ~techlevel.high
		Queue: VehicleSQ, VehicleMQ
		Description: Long-range hover artillery.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft\n• Has difficulty hitting moving targets
		Attributes: • Can traverse water
	Mobile:
		Speed: 72
		TurnSpeed: 512
		Locomotor: lighthover
	Health:
		HP: 15000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: RuinerCannon
		LocalOffset: 650,0,600
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		PauseOnCondition: (ammo || charged-fire) && !uncharged-fire
	Armament@PRIMARYCHARGED:
		Name: primary-charged
		Weapon: RuinerCannonCharged
		LocalOffset: 650,0,600
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		PauseOnCondition: !ammo || uncharged-fire
	WithMuzzleOverlay:
	FireWarheadsOnDeath:
		Weapon: ArtilleryExplode
		LoadedChance: 75
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	TurretedFloating:
		TurnSpeed: 16
		Offset: 0,0,0
		RealignDelay: 75
	AttackTurreted:
		ForceFireIgnoresActors: True
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		Armaments: primary, primary-charged
	WithSpriteTurret:
	AmmoPool@RESCONV:
		Ammo: 12
	ReloadAmmoPool@RESCONV:
		Count: 3
	WithAmmoPipsDecoration@RESCONV:
		PipCount: 4
	GrantCondition@RESCONVCHARGE:
		RequiresCondition: resconv-upgrade && on-resource && (ammo < 12 || charged-fire)
	GrantConditionOnAttack@PRIMARY:
		RevokeDelay: 175
	GrantConditionOnAttack@PRIMARYCHARGED:
		RevokeDelay: 175
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater

ATMZ:
	Inherits: ^Vehicle
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Valued:
		Cost: 1350
	Tooltip:
		Name: Atomizer
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 285
		Prerequisites: scrt, ~vehicles.collector, ~techlevel.high
		Description: Lightly armored long-range anti-tank unit\n  that atomizes vehicles and defenses.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses
		Weaknesses: • Weak vs Infantry, Buildings\n• Cannot attack Aircraft
		Attributes: • Atomizes up to 4 targets\n• Affected targets take damage over time\n• Affected targets have reduced speed and firepower\n• Can traverse water
	Mobile:
		Speed: 72
		Locomotor: lighthover
	Health:
		HP: 18000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 32
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	Armament:
		Weapon: AtomizerBolts
		LocalOffset: 350,0,350
	AttackTurreted:
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
	WithSpriteTurret:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater

TPOD:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@ONRESOURCE: ^OnResourceConditions
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 281
		Prerequisites: scrt, ~vehicles.tpod, ~techlevel.high
		Description: Heavy assault walker with beam weapons.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Can crush concrete walls.\n• Self repairs on Tiberium or other resources when not in combat.
	Voiced:
		VoiceSet: TripodVoice
	Valued:
		Cost: 1800
	Selectable:
		Bounds: 1280, 2218, 0, -426
	Tooltip:
		Name: Annihilator Tripod
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 82000
	Armor:
		Type: Heavy
	Mobile:
		Locomotor: heavytracked
		Speed: 44
		TurnSpeed: 512
	Passenger:
		Weight: 3
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: TripodLaser
		LocalOffset: 650,420,1000
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	Armament@SECONDARY:
		Name: secondary
		Weapon: TripodLaserReversed
		LocalOffset: 650,-420,1000
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		FireDelay: 20
	WithMuzzleOverlay:
	Carryable:
		LocalOffset: 0,0,700
	-Crushable:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	TurretedFloating:
		TurnSpeed: 12
		Offset: 0,0,0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithSpriteTurret:
	WithMoveAnimation:
	DeathSounds:
		Voice: Die
	SpawnActorOnDeath:
		Actor: TPOD.Husk
		RequiresCondition: !being-warped
	GrantConditionOnFaction@TRAVELERSPEED:
		Condition: travelerspeed
		Factions: traveler
	SpeedMultiplier@TRAVELERSPEED:
		RequiresCondition: travelerspeed
		Modifier: 115
	WithIdleOverlay@MINDCONTROL:
		Offset: 0,0,1024
	ChangesHealth@THEAL:
		Step: 0
		PercentageStep: 1
		Delay: 15
		StartIfBelow: 100
		DamageCooldown: 150
		RequiresCondition: on-resource
	WithDecoration@TibRepair:
		Image: select
		Palette: chrome
		Sequence: repair-small
		Position: Center
		RequiresCondition: on-resource && damaged
		BlinkInterval: 15
		BlinkPattern: on, off

RTPD:
	Inherits: TPOD
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@RESCONV: ^ResourceConversion
	Buildable:
		BuildPaletteOrder: 282
		Prerequisites: scrt, ~vehicles.reaper, ~techlevel.high
	TooltipExtras:
		Attributes: • Can crush concrete walls.\n• Weapons empowered by Tiberium or other resources\n• Self repairs on Tiberium or other resources when not in combat.
	Valued:
		Cost: 2200
	Tooltip:
		Name: Reaper Tripod
	Health:
		HP: 98000
	SpawnActorOnDeath:
		Actor: RTPD.Husk
	AttackTurreted:
		Armaments: primary, secondary, primary-charged, secondary-charged
	Armament@PRIMARY:
		Weapon: ReaperLaser
		LocalOffset: 700,550,1000
		PauseOnCondition: (ammo || charged-fire) && !uncharged-fire
	Armament@SECONDARY:
		Weapon: ReaperLaserReversed
		LocalOffset: 700,-550,1000
		FireDelay: 15
		PauseOnCondition: (ammo || charged-fire) && !uncharged-fire
	Armament@PRIMARYCHARGED:
		Name: primary-charged
		Weapon: ReaperLaserCharged
		LocalOffset: 700,550,1000
		PauseOnCondition: !ammo || uncharged-fire
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		AmmoUsage: 2
		RequiresCondition: !resconv-upgrade
	Armament@SECONDARYCHARGED:
		Name: secondary-charged
		Weapon: ReaperLaserChargedReversed
		LocalOffset: 700,-550,1000
		FireDelay: 15
		PauseOnCondition: !ammo || uncharged-fire
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		AmmoUsage: 2
		RequiresCondition: !resconv-upgrade
	Armament@PRIMARYCHARGEDUPG:
		Name: primary-charged
		Weapon: ReaperLaserCharged
		LocalOffset: 700,550,1000
		PauseOnCondition: !ammo || uncharged-fire
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		RequiresCondition: resconv-upgrade
	Armament@SECONDARYCHARGEDUPG:
		Name: secondary-charged
		Weapon: ReaperLaserChargedReversed
		LocalOffset: 700,-550,1000
		FireDelay: 15
		PauseOnCondition: !ammo || uncharged-fire
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		RequiresCondition: resconv-upgrade
	AmmoPool@RESCONV:
		Ammo: 20
		Armaments: primary-charged, secondary-charged
	ReloadAmmoPool@RESCONV:
		Count: 4
	WithAmmoPipsDecoration@RESCONV:
		PipCount: 5
	GrantCondition@RESCONVCHARGE:
		RequiresCondition: on-resource && (ammo < 20 || charged-fire)
	GrantConditionOnAttack@PRIMARY:
		RevokeDelay: 50
	GrantConditionOnAttack@PRIMARYCHARGED:
		RevokeDelay: 50
	WithIdleOverlay@RESCONVTIB:
		Offset: 0,0,200
	WithIdleOverlay@RESCONVORE:
		Offset: 0,0,200
	WithIdleOverlay@RESCONVBLUETIB:
		Offset: 0,0,200
	WithIdleOverlay@RESCONVGEMS:
		Offset: 0,0,200

OBLT:
	Inherits: ^Vehicle
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 281
		Prerequisites: scrt, ~vehicles.harbinger, ~techlevel.high
		Description: Heavy artillery that creates a charged particle stream through which\n  it projects a powerful energy pulse, damaging everything it passes through.
		IconPalette: chromes
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Light Armor, Infantry
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
		Attributes: • Can traverse water
	Valued:
		Cost: 2300
	Tooltip:
		Name: Obliterator
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 30000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 512
		Speed: 48
		Locomotor: lighthover
	TurretedFloating:
		TurnSpeed: 10
		Offset: 0,0,0
		RealignDelay: 100
	WithSpriteTurret:
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: ObliteratorBolt
		LocalOffset: 750,0,400
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		PauseOnCondition: !ammo
		ReloadingCondition: reloading
	Armament@SECONDARY:
		Name: secondary
		Weapon: ObliteratorCharge
		LocalOffset: 750,0,400
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		PauseOnCondition: reloading
	AmbientSoundCA@CHARGING:
		SoundFiles: oblt-charge2.aud
		InitialSound: oblt-charge1.aud
		RequiresCondition: charging
		AudibleThroughFog: true
	GrantConditionOnAttack@CHARGING:
		Condition: charging
		ArmamentNames: secondary
		RevokeDelay: 6
	AmmoPool:
		Ammo: 1
		InitialAmmo: 0
		AmmoCondition: ammo
		Armaments: primary
	ReloadAmmoPoolCA@CHARGE:
		Delay: 100
		Count: 1
		RequiresCondition: charging && !reloading
		ShowSelectionBar: true
		SelectionBarColor: ff00ff
		DrainAmountOnDisabled: 2
	ReloadAmmoPoolCA@DRAIN:
		Count: -1
		Delay: 1
		RequiresCondition: !charging
		ShowSelectionBar: false
	WithMuzzleOverlay:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		Armaments: primary, secondary
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater
	SpawnActorOnDeath:
		Actor: OBLT.Husk
		RequiresCondition: !being-warped
	Targetable@Obliterator:
		TargetTypes: Obliterator

NULL:
	Inherits: ^Vehicle
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 291
		Prerequisites: scrt, ~vehicles.scrin, ~rebel.allegiance, ~techlevel.high
		Description: Heavy hover tank that disables enemy weapons and vision.
		IconPalette: chromes
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
		Attributes: • Disables enemy weapons and vision\n• Can traverse water
	Valued:
		Cost: 1600
	Tooltip:
		Name: Nullifier
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 25000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 512
		Speed: 60
		Locomotor: lighthover
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: NullifierBolt
		LocalOffset: 750,0,300
		MuzzleSequence: muzzle
	WithMuzzleOverlay:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	TurretedFloating:
		TurnSpeed: 16
		Offset: 0,0,0
		RealignDelay: 100
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithSpriteTurret:
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater
	SpawnActorOnDeath:
		Actor: NULL.Husk
		RequiresCondition: !being-warped

SMCV:
	Inherits: ^Vehicle-NOUPG
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@SELECTION: ^SelectableSupportUnit
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Inherits@EconomyPolicyDiscount: ^EconomyPolicyDiscount
	Inherits@BLD: ^ProducesBuildings
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 602
		Prerequisites: vehicles.mcv, ~vehicles.scrin
		IconPalette: chrome
		Description: Deploys into another Colony Platform.
		BuildDurationModifier: 50
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Special Ability: Deploy into Colony Platform\n• Can traverse water
	Valued:
		Cost: 3000
	Tooltip:
		Name: Colony Ship
	Selectable:
		Priority: 4
		Bounds: 1792, 1792
	Health:
		HP: 75000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 54
		Locomotor: lighthover
		TurnSpeed: 512
	RevealsShroud:
		Range: 4c0
	Transforms:
		IntoActor: sfac
		Offset: -1, 0
		Facing: 384
		TransformSounds: placbldg.aud, build5.aud
		NoTransformNotification: BuildingCannotPlaceAudio
		NoTransformTextNotification: Cannot deploy here.
		PauseOnCondition: empdisable || being-warped
	MustBeDestroyed:
		RequiredForShortGame: true
	BaseBuilding:
	ProvidesPrerequisite@anymcv:
		Prerequisite: anymcv
	SpawnActorOnDeath:
		Actor: SMCV.Husk
		RequiresCondition: !being-warped
	TransferTimedExternalConditionOnTransform:
		Condition: invulnerability
	TransferTimedExternalConditionOnTransform@INVIS:
		Condition: invisibility
	-Crushable:
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	DamageTypeDamageMultiplier@A2GPROTECTION:
		Modifier: 60
	UpdatesKillCount@BuildingsOrHarvesters:
		Type: BuildingsOrHarvesters
	-PrimaryBuilding:
	-WithDecoration@primary:
	GrantConditionOnPrerequisite@Rebel:
		Prerequisites: rebel.allegiance
		Condition: rebel-allegiance
	ProvidesPrerequisite@scrin:
		Prerequisite: structures.scrin
		RequiresCondition: rebel-allegiance
	ProvidesPrerequisiteValidatedFaction@reaper:
		Factions: reaper
		Prerequisite: structures.reaper
		RequiresCondition: rebel-allegiance
	ProvidesPrerequisiteValidatedFaction@traveler:
		Factions: traveler
		Prerequisite: structures.traveler
		RequiresCondition: rebel-allegiance
	ProvidesPrerequisiteValidatedFaction@harbinger:
		Factions: harbinger
		Prerequisite: structures.harbinger
		RequiresCondition: rebel-allegiance
	ProvidesPrerequisiteValidatedFaction@collector:
		Factions: collector
		Prerequisite: structures.collector
		RequiresCondition: rebel-allegiance
	ProvidesPrerequisite@anyconyard:
		Prerequisite: anyconyard
		RequiresCondition: rebel-allegiance
	FreeActor@QUEUEUPDATER:
		RequiresCondition: updatequeue && rebel-allegiance
	-SpawnActorOnCapture@QUEUEUPDATER:
	-GrantConditionOnPrerequisite@MQF:
	-GrantConditionOnPrerequisite@MQS:
	GrantDelayedCondition@QUEUEUPDATER:
		RequiresCondition: rebel-allegiance
	Production@SQBLD:
		RequiresCondition: !global-multiqueuefull && rebel-allegiance
		PauseOnCondition: being-warped
	Production@MQBLD:
		RequiresCondition: global-multiqueuefull && rebel-allegiance
		PauseOnCondition: being-warped
	ProductionBar@SQBLD:
		RequiresCondition: !global-multiqueuefull && rebel-allegiance
	ProductionBar@SQDEF:
		RequiresCondition: !global-multiqueuefull && rebel-allegiance
	ProductionBar@MQBLD:
		RequiresCondition: global-multiqueuefull && rebel-allegiance
	ProductionBar@MQDEF:
		RequiresCondition: global-multiqueuefull && rebel-allegiance

#
# ---- Aircraft
#

STMR:
	Inherits: ^Helicopter
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@HULLREGEN: ^HullRegen
	Inherits@SHIELDS: ^ScrinShields
	Inherits@MothershipRearmable: ^MothershipRearmable
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Buildable:
		Queue: AircraftSQ, AircraftMQ
		BuildPaletteOrder: 60
		Prerequisites: ~aircraft.scrin, ~techlevel.medium
		Description: Durable patrol craft.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Buildings, Aircraft
		Weaknesses: • Weak vs Defenses
	Valued:
		Cost: 1650
	Tooltip:
		Name: Stormrider
		GenericName: Aircraft
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 26000
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	Armament@PRIMARY:
		Weapon: StormriderZap
		LocalOffset: 350,300,0, 350,-300,0
		PauseOnCondition: !ammo
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	Armament@SECONDARY:
		Name: secondary
		Weapon: StormriderZapAA
		LocalOffset: 350,300,0, 350,-300,0
		PauseOnCondition: !ammo
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	WithMuzzleOverlay:
	AttackAircraft:
		FacingTolerance: 512
		PersistentTargeting: false
		OpportunityFire: true
		PauseOnCondition: empdisable || being-warped || blinded
	Aircraft:
		CruiseAltitude: 2560
		TurnSpeed: 22
		Speed: 146
		IdleSpeed: 135
		IdleTurnSpeed: 15
		RepulsionSpeed: 40
		MaximumPitch: 56
		CanHover: False
		TakeoffSounds: dropup1.aud
		LandingSounds: dropdwn1.aud
		CanSlide: False
		AltitudeVelocity: 0c120
	RangeMultiplier@Volley:
		Modifier: 130
		RequiresCondition: volley
	GrantConditionOnAttack@Volley:
		ArmamentNames: primary, secondary
		Condition: volley
		RevokeDelay: 5
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: AttackAnything
	Contrail@1:
		Offset: -250,-250,100
		StartColorUsePlayerColor: true
		StartColorAlpha: 64
		EndColorUsePlayerColor: true
		ZOffset: -512
	Contrail@2:
		Offset: -250,250,100
		StartColorUsePlayerColor: true
		StartColorAlpha: 64
		EndColorUsePlayerColor: true
		ZOffset: -512
	SpawnActorOnDeath:
		Actor: STMR.Husk
		RequiresCondition: !empdisable && !being-warped
	SpawnActorOnDeath@EMP:
		Actor: STMR.Husk.EMP
		RequiresCondition: empdisable && !being-warped
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
		RequiresCondition: enable-smoke
	Selectable:
		DecorationBounds: 1536, 1280
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded
	-EjectOnDeath:
	AmmoPool:
		Ammo: 15
		AmmoCondition: ammo
		ReloadDelay: 45
		ReloadCount: 5
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 3
	Rearmable:
		RearmActors: hpad, hpad.td, afld, afld.gdi, grav
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	GrantConditionOnResupply:
		Condition: resupplying
	Targetable@RESUPPLYING:
		TargetTypes: Resupplying
		RequiresCondition: resupplying
	KillsSelf@Emp:
		RequiresCondition: empdisable && cruising && !shields-upgrade
	SpeedMultiplier@EMPDISABLE:
		Modifier: 0
		RequiresCondition: empdisable
	GpsRadarDot:
		Sequence: Plane
	-SoundOnDamageTransitionCA:

TORM:
	Inherits: ^Helicopter
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@HULLREGEN: ^HullRegen
	Inherits@SHIELDS: ^ScrinShields
	Inherits@MothershipRearmable: ^MothershipRearmable
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Buildable:
		Queue: AircraftSQ, AircraftMQ
		BuildPaletteOrder: 61
		Prerequisites: ~aircraft.scrin, ~techlevel.medium
		Description: Small anti-vehicle harrasser aircraft.
		IconPalette: chromes
	TooltipExtras:
		Strengths: • Strong vs Vehicles, Defenses, Aircraft
		Weaknesses: • Weak vs Infantry
	Valued:
		Cost: 900
	Tooltip:
		Name: Tormentor
		GenericName: Aircraft
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 10000
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	Armament@PRIMARY:
		Weapon: TormentorZap
		LocalOffset: 450,300,100, 450,-300,100
		PauseOnCondition: !ammo
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	Armament@SECONDARY:
		Weapon: TormentorZapAA
		LocalOffset: 450,300,100, 450,-300,100
		PauseOnCondition: !ammo
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	WithMuzzleOverlay:
	AttackAircraft:
		FacingTolerance: 80
		PersistentTargeting: false
		OpportunityFire: true
		PauseOnCondition: empdisable || being-warped || blinded
	Aircraft:
		CruiseAltitude: 2560
		TurnSpeed: 32
		Speed: 201
		IdleSpeed: 146
		IdleTurnSpeed: 15
		RepulsionSpeed: 40
		MaximumPitch: 56
		CanHover: False
		TakeoffSounds: dropup1.aud
		LandingSounds: dropdwn1.aud
		CanSlide: False
		AltitudeVelocity: 0c120
		IdealSeparation: 1c256
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: AttackAnything
	Contrail@1:
		Offset: -750,-200,300
		StartColorUsePlayerColor: true
		StartColorAlpha: 64
		EndColorUsePlayerColor: true
		ZOffset: -512
		TrailLength: 18
		StartWidth: 48
	Contrail@2:
		Offset: -750,200,300
		StartColorUsePlayerColor: true
		StartColorAlpha: 64
		EndColorUsePlayerColor: true
		ZOffset: -512
		TrailLength: 18
		StartWidth: 48
	SpawnActorOnDeath:
		Actor: TORM.Husk
		RequiresCondition: !empdisable && !being-warped
	SpawnActorOnDeath@EMP:
		Actor: TORM.Husk.EMP
		RequiresCondition: empdisable && !being-warped
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
		RequiresCondition: enable-smoke
	Selectable:
		DecorationBounds: 1536, 1536
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded
	-EjectOnDeath:
	AmmoPool:
		Ammo: 12
		AmmoCondition: ammo
		ReloadDelay: 10
		ReloadCount: 1
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 4
	Rearmable:
		RearmActors: hpad, hpad.td, afld, afld.gdi, grav
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	GrantConditionOnResupply:
		Condition: resupplying
	Targetable@RESUPPLYING:
		TargetTypes: Resupplying
		RequiresCondition: resupplying
	KillsSelf@Emp:
		RequiresCondition: empdisable && cruising && !shields-upgrade
	SpeedMultiplier@EMPDISABLE:
		Modifier: 0
		RequiresCondition: empdisable
	Shielded:
		MaxStrength: 4000
	GpsRadarDot:
		Sequence: Plane
	Targetable@MothershipRearmable:
		RequiresCondition: ammo < 12
	ReloadAmmoPool@MothershipRearmable:
		Count: 4
	-SoundOnDamageTransitionCA:

ENRV:
	Inherits: ^Helicopter
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@HULLREGEN: ^HullRegen
	Inherits@SHIELDS: ^ScrinShields
	Inherits@MothershipRearmable: ^MothershipRearmable
	Inherits@SCRINVEHICLEVOICE: ^ScrinVehicleVoice
	Buildable:
		Queue: AircraftSQ, AircraftMQ
		BuildPaletteOrder: 110
		Prerequisites: scrt, ~aircraft.traveler, ~techlevel.high
		Description: Fast attack craft that creates a focused suppression field.
		IconPalette: chromes
	TooltipExtras:
		Strengths: • Strong vs Vehicles, Defenses, Aircraft
		Weaknesses: • Weak vs Infantry
		Attributes: • Slows targets
	Valued:
		Cost: 2000
	Tooltip:
		Name: Enervator
		GenericName: Aircraft
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 22500
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 12c0
		Type: GroundPosition
	Armament@PRIMARY:
		Weapon: EnervatorBolts
		LocalOffset: -300,0,-250
		PauseOnCondition: !ammo
	Armament@SECONDARY:
		Weapon: EnervatorBoltsAA
		LocalOffset: -300,0,-250
		PauseOnCondition: !ammo
	AttackAircraft:
		FacingTolerance: 40
		PersistentTargeting: false
		OpportunityFire: false
		PauseOnCondition: empdisable || being-warped || blinded
	Aircraft:
		TurnSpeed: 24
		Speed: 225
		InitialFacing: 768
		TakeoffSounds: dropup1.aud
		LandingSounds: dropdwn1.aud
		CruiseAltitude: 2560
		IdealSeparation: 1c256
		MaximumPitch: 56
		CanSlide: True
		AltitudeVelocity: 0c100
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: AttackAnything
	Contrail@1:
		Offset: -750,-200,300
		StartColorUsePlayerColor: true
		StartColorAlpha: 64
		EndColorUsePlayerColor: true
		ZOffset: -512
		TrailLength: 18
		StartWidth: 48
	Contrail@2:
		Offset: -750,200,300
		StartColorUsePlayerColor: true
		StartColorAlpha: 64
		EndColorUsePlayerColor: true
		ZOffset: -512
		TrailLength: 18
		StartWidth: 48
	SpawnActorOnDeath:
		Actor: ENRV.Husk
		RequiresCondition: !empdisable && !being-warped
	SpawnActorOnDeath@EMP:
		Actor: ENRV.Husk.EMP
		RequiresCondition: empdisable && !being-warped
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
		RequiresCondition: enable-smoke
	Selectable:
		DecorationBounds: 1536, 1536
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded
	-EjectOnDeath:
	AmmoPool:
		Ammo: 5
		AmmoCondition: ammo
		ReloadDelay: 35
		ReloadCount: 1
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 5
	Rearmable:
		RearmActors: hpad, hpad.td, afld, afld.gdi, grav
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	GrantConditionOnResupply:
		Condition: resupplying
	Targetable@RESUPPLYING:
		TargetTypes: Resupplying
		RequiresCondition: resupplying
	KillsSelf@Emp:
		RequiresCondition: empdisable && cruising && !shields-upgrade
	SpeedMultiplier@EMPDISABLE:
		Modifier: 0
		RequiresCondition: empdisable
	Shielded:
		MaxStrength: 5625
	GpsRadarDot:
		Sequence: Plane
	Targetable@MothershipRearmable:
		RequiresCondition: ammo < 5
	ReloadAmmoPool@MothershipRearmable:
		Count: 1
	-SoundOnDamageTransitionCA:

DEVA:
	Inherits: ^Helicopter
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@HULLREGEN: ^HullRegen
	Inherits@SHIELDS: ^ScrinShields
	Inherits@FleetRecallable: ^FleetRecallable
	Inherits@SignalTransmitterSpeedBoost: ^SignalTransmitterSpeedBoost
	Buildable:
		Queue: AircraftSQ, AircraftMQ
		BuildPaletteOrder: 111
		Prerequisites: scrt, ~aircraft.scrin, ~techlevel.high
		Description: Long-range siege warship.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft\n• Has difficulty hitting moving targets
	Voiced:
		VoiceSet: DevastatorVoice
	Valued:
		Cost: 2500
	Tooltip:
		Name: Devastator Warship
		GenericName: Aircraft
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 32000
	Armor:
		Type: Aircraft
	HitShape:
		TargetableOffsets: 0,0,0, -512,0,0, -1024,0,0
		Type: Circle
			Radius: 1c256
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	Armament@PRIMARY:
		Weapon: DevastatorDiscs
		LocalOffset: 500,0,0
		RequiresCondition: !stellar-upgrade
	Armament@PRIMARYUPG:
		Name: primary-upg
		Weapon: DevastatorTorpedo
		LocalOffset: 500,0,0
		RequiresCondition: stellar-upgrade
	AmbientSound@CHARGE:
		RequiresCondition: charging
		SoundFiles: plasmatorpcharge.aud
	WithIdleOverlay@CHARGE1:
		Image: deva
		Sequence: charge1
		Palette: scrin
		Offset: 500,0,0
		RequiresCondition: charging == 1
	WithIdleOverlay@CHARGE2:
		Image: deva
		Sequence: charge2
		Palette: scrin
		Offset: 500,0,0
		RequiresCondition: charging == 2
	WithIdleOverlay@CHARGE3:
		Image: deva
		Sequence: charge3
		Palette: scrin
		Offset: 500,0,0
		RequiresCondition: charging > 2
	GrantConditionOnPrerequisite@STELLAR:
		Condition: stellar-upgrade
		Prerequisites: stellar.upgrade
	AttackFrontal:
		Armaments: primary
		FacingTolerance: 40
		ForceFireIgnoresActors: True
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		RequiresCondition: !stellar-upgrade
	AttackFrontalCharged:
		Armaments: primary-upg
		FacingTolerance: 40
		ForceFireIgnoresActors: True
		TargetFrozenActors: True
		ChargeLevel: 100
		DischargeRate: 2
		ChargingCondition: charging
		ConditionChargeLevels: 1, 34, 67
		ShotsPerCharge: 3
		ShowSelectionBar: true
		SelectionBarColor: cc33ff
		PauseOnCondition: empdisable || being-warped || blinded
		RequiresCondition: stellar-upgrade
	Aircraft:
		CruiseAltitude: 2c768
		InitialFacing: 768
		TurnSpeed: 16
		Speed: 60
		AltitudeVelocity: 0c50
		CanForceLand: False
	Selectable:
		Bounds: 1536, 1194, 0, 85
		DecorationBounds: 1706, 1706, 0, 42
	FireWarheadsOnDeath:
		Weapon: KirovExplode
		RequiresCondition: !airborne
	SpawnActorOnDeath:
		Actor: DEVA.Husk
		RequiresCondition: !empdisable && !being-warped
	SpawnActorOnDeath@EMP:
		Actor: DEVA.Husk.EMP
		RequiresCondition: empdisable && !being-warped
	-Hovers@CRUISING:
	-EjectOnDeath:
	ProductionCostMultiplier@HARBONUS:
		Multiplier: 90
		Prerequisites: player.harbinger
	KillsSelf@Emp:
		RequiresCondition: empdisable && cruising && !shields-upgrade
	SpeedMultiplier@EMPDISABLE:
		Modifier: 0
		RequiresCondition: empdisable
	Repairable:
		RepairActors: fix, rep, srep
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded
	Turreted@SHIELDS:
		Offset: -64,0,0
	GpsRadarDot:
		Sequence: Plane
	-SoundOnDamageTransitionCA:
	Targetable@AirHighValue:
		TargetTypes: AirHighValue

PAC:
	Inherits: ^Helicopter
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@HULLREGEN: ^HullRegen
	Inherits@SHIELDS: ^ScrinShields
	Inherits@FleetRecallable: ^FleetRecallable
	Inherits@SignalTransmitterSpeedBoost: ^SignalTransmitterSpeedBoost
	Buildable:
		Queue: AircraftSQ, AircraftMQ
		BuildPaletteOrder: 112
		Prerequisites: sign, scrt, ~aircraft.scrin, ~techlevel.high
		Description: Warship that launches a squadron\n  of Invader assault craft.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Weak vs Buildings, Defenses
		Attributes: • Invaders only targetable by static anti-air defenses
	Voiced:
		VoiceSet: PlanetaryAssaultCarrierVoice
	Valued:
		Cost: 2800
	Tooltip:
		Name: Planetary Assault Carrier
		GenericName: Aircraft
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 52000
	Armor:
		Type: Aircraft
	HitShape:
		TargetableOffsets: 0,0,0, 1024,0,0, -1024,0,0
		Type: Circle
			Radius: 1c256
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	Armament@PRIMARY:
		Weapon: InvaderLauncher
		LocalOffset: 0,0,0
	Aircraft:
		CruiseAltitude: 2c768
		InitialFacing: 768
		TurnSpeed: 16
		Speed: 60
		AltitudeVelocity: 0c50
		CanForceLand: False
	Selectable:
		Bounds: 1536, 1194, 0, 85
		DecorationBounds: 1706, 1706, 0, 42
	FireWarheadsOnDeath:
		Weapon: KirovExplode
		RequiresCondition: !airborne
	SpawnActorOnDeath:
		Actor: PAC.Husk
		RequiresCondition: !empdisable && !being-warped
	SpawnActorOnDeath@EMP:
		Actor: PAC.Husk.EMP
		RequiresCondition: empdisable && !being-warped
	-Hovers@CRUISING:
	WithFacingSpriteBody:
		RequiresCondition: inva-loaded == 6
		Name: idle
	WithFacingSpriteBody@HALF:
		RequiresCondition: inva-loaded < 6 && inva-loaded > 0
		Sequence: half-deployed
		Name: half-deployed
	WithFacingSpriteBody@EMPTY:
		RequiresCondition: inva-loaded == 0
		Sequence: fully-deployed
		Name: fully-deployed
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	Turreted:
		TurnSpeed: 512
		RealignDelay: 0
	CarrierMaster@carr1:
		Actors: inva, inva, inva, inva, inva, inva
		RearmTicks: 0
		RespawnTicks: 375
		InstantRepair: true
		SlaveDisposalOnKill: KillSlaves
		SpawnAllAtOnce: false
		LoadedCondition: inva-loaded
		RequiresCondition: !empdisable && !being-warped
		MaxSlaveDistance: 16c0
	WithSpawnerMasterPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	Exit@0:
		SpawnOffset: -100,0,0
		Facing: 240
	Exit@1:
		SpawnOffset: -100,0,0
		Facing: -240
	Exit@2:
		SpawnOffset: 100,0,0
		Facing: 240
	Exit@3:
		SpawnOffset: 100,0,0
		Facing: -240
	Exit@4:
		SpawnOffset: 0,0,0
		Facing: 384
	Exit@5:
		SpawnOffset: 0,0,0
		Facing: -384
	DeathSounds:
		Voice: Die
	-EjectOnDeath:
	KillsSelf@Emp:
		RequiresCondition: empdisable && cruising && !shields-upgrade
	SpeedMultiplier@EMPDISABLE:
		Modifier: 0
		RequiresCondition: empdisable
	Repairable:
		RepairActors: fix, rep, srep
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded
	Shielded:
		MaxStrength: 13000
	Turreted@SHIELDS:
		Offset: -128,0,0
	GpsRadarDot:
		Sequence: Plane
	-SoundOnDamageTransitionCA:
	Targetable@AirHighValue:
		TargetTypes: AirHighValue

INVA:
	Inherits: ^NeutralPlane
	Inherits@3: ^Cloakable
	Inherits@4: ^EmpDisable
	Inherits@5: ^GDIUpgradeAircraft
	Inherits@ionsurge: ^IonSurgable
	Inherits@FleetRecallable: ^FleetRecallable
	Valued:
		Cost: 50
	Tooltip:
		Name: Invader
	Health:
		HP: 22000
	RevealsShroud:
		Range: 2c0
		MinRange: 1c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 1c0
		Type: GroundPosition
	Armament@PRIMARY:
		Weapon: InvaderZap
	Armament@SECONDARY:
		Name: secondary
		Weapon: InvaderZapAA
	AttackAircraft:
		FacingTolerance: 80
		PersistentTargeting: false
		OpportunityFire: false
		PauseOnCondition: empdisable
		AttackType: Strafe
		StrafeRunLength: 3c0
	Aircraft:
		CruiseAltitude: 2560
		InitialFacing: 768
		TurnSpeed: 32
		Speed: 220
		MaximumPitch: 56
		CanHover: True
		CanSlide: False
		RepulsionSpeed: 40
		Repulsable: False
		IdealSeparation: 0c256
		VTOL: true
		TakeoffSounds: invader-launch1.aud, invader-launch2.aud
		LandingSounds: invader-dock.aud
	RejectsOrders:
	-SpawnActorOnDeath:
	FireWarheadsOnDeath:
		Weapon: VisualExplodeInvader
		-RequiresCondition:
	-Selectable:
	-ActorLostNotification:
	LeavesTrails:
		Offsets: -253,0,171
		MovingInterval: 2
		Image: smokey
		StationaryInterval: 2
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
		RequiresCondition: enable-smoke
	CarrierSlave:
	Contrail@1:
		Offset: -432,0,0
		StartColorUsePlayerColor: true
		EndColorUsePlayerColor: true
		StartColorAlpha: 64
		TrailLength: 10
	-Contrail@2:
	Interactable:
	GrantConditionOnPrerequisite@SHIELDS:
		Condition: shields-upgrade
		Prerequisites: shields.upgrade
	DamageMultiplier@SHIELDS:
		RequiresCondition: shields-upgrade
		Modifier: 75
	Targetable@AIRBORNE:
		RequiresCondition: airborne
		TargetTypes: ICBM
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	GivesExperienceToMaster:

MSHP:
	Inherits: ^Helicopter
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@HULLREGEN: ^HullRegen
	Inherits@SHIELDS: ^ScrinShields
	Inherits@FleetRecallable: ^FleetRecallable
	Inherits@SignalTransmitterSpeedBoost: ^SignalTransmitterSpeedBoost
	Buildable:
		Queue: AircraftSQ, AircraftMQ
		BuildPaletteOrder: 113
		Prerequisites: sign, scrt, ~aircraft.scrin, ~techlevel.high
		Description: Huge craft with powerful anti-structure beam weapon\n  and secondary plasma weapons.
		IconPalette: chromes
		BuildLimit: 1
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft\n• Long charge time before firing anti-structure weapon\n• Cannot move while firing
		Attributes: • Maximum 1 can be built\n• Can create wormholes\n• Can recharge nearby Stormriders/Enervators
	Voiced:
		VoiceSet: MothershipVoice
	Valued:
		Cost: 3000
	Tooltip:
		Name: Mothership
		GenericName: Aircraft
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 130000
	Armor:
		Type: Aircraft
	HitShape:
		TargetableOffsets: 0,0,0, 2560,0,0, -2560,0,0, 0,2560,0, 0,-2560,0
		Type: Circle
			Radius: 2c768
	RevealsShroud:
		Range: 13c0
		MinRange: 11c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 11c0
		Type: GroundPosition
	Armament@PRIMARY:
		Weapon: MothershipBeam
		Turret: none
		LocalOffset: 0,0,-940
		PauseOnCondition: !ammo
		ReloadingCondition: cooldown
	Armament@SECONDARY:
		Name: secondary
		Turret: none
		Weapon: MothershipChargeBeam
		LocalOffset: 0,0,-940
		PauseOnCondition: attacking || ammo || cooldown
	Armament@TERTIARY:
		Name: tertiary
		Turret: body
		Weapon: MothershipDiscs
		LocalOffset: 400,0,-200, 400,0,50, 400,0,300
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	WithMuzzleOverlay:
	WithShadow:
		ZOffset: -3072
	Aircraft:
		CruiseAltitude: 3c512
		InitialFacing: 768
		TurnSpeed: 512
		Speed: 60
		AltitudeVelocity: 0c50
		CanForceLand: False
		Voice: Move
	TurretedFloating@BODY:
		Turret: body
		TurnSpeed: 16
		Offset: 0,0,0
		RealignDelay: 50
	WithSpriteTurret@BODY:
		Turret: body
		Sequence: idle
	Selectable:
		Bounds: 1792, 1792, 0, 85
		DecorationBounds: 2048, 2218, 0, 42
	FireWarheadsOnDeath:
		Weapon: KirovExplode
		RequiresCondition: !airborne
	SpawnActorOnDeath:
		Actor: MSHP.Husk
		RequiresCondition: !empdisable && !being-warped
	SpawnActorOnDeath@EMP:
		Actor: MSHP.Husk.EMP
		RequiresCondition: empdisable && !being-warped
	-Hovers@CRUISING:
	WithFacingSpriteBody:
		Sequence: empty
	AttackTurreted:
		Armaments: primary, secondary, tertiary
		Voice: Attack
		PauseOnCondition: empdisable || being-warped || blinded
		ForceFireIgnoresActors: true
		Turrets: body
	-EjectOnDeath:
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded
	WithIdleOverlay@RING:
		Image: mshp
		Sequence: ring
		Offset: 0,0,72
		PauseOnCondition: being-warped
	ExternalCondition@PRODUCED:
		Condition: produced
	VoiceAnnouncement:
		RequiresCondition: produced
		Voice: Build
	DeathSounds:
		Voice: Die
	AnnounceOnCreation:
		SpeechNotification: MothershipDeployed
		Delay: 30
	AmbientSoundCA:
		SoundFiles: mshp-beamloop.aud
		FinalSound: mshp-beamend.aud
		RequiresCondition: attacking
	GrantConditionOnAttack@ATTACKING:
		Condition: attacking
		ArmamentNames: primary
		RevokeDelay: 125
	AmbientSoundCA@CHARGING:
		SoundFiles: mshp-charge.aud
		RequiresCondition: charging
	GrantConditionOnAttack@CHARGING:
		Condition: charging
		ArmamentNames: secondary
		RevokeDelay: 5
	SpeedMultiplier@ATTACKING:
		Modifier: 0
		RequiresCondition: attacking
	AmmoPool:
		Ammo: 1
		InitialAmmo: 0
		AmmoCondition: ammo
		Armaments: primary
	ReloadAmmoPoolCA@CHARGE:
		Delay: 100
		Count: 1
		RequiresCondition: charging && !attacking
		Sound: mshp-fire1.aud
		ShowSelectionBar: true
		SelectionBarColor: ff00ff
	ReloadAmmoPoolCA@DRAIN:
		Count: -1
		Delay: 1
		RequiresCondition: !charging && !attacking
		ShowSelectionBar: false
	KillsSelf@Emp:
		RequiresCondition: empdisable && cruising && !shields-upgrade
	SpeedMultiplier@EMPDISABLE:
		Modifier: 0
		RequiresCondition: empdisable
	Shielded:
		MaxStrength: 26000
	SpawnActorAbility:
		Actors: wormhole
		SkipMakeAnimations: false
		Range: 18c0
		CircleColor: 9988ff
		SpawnSounds: wormhole-open.aud
		SelectTargetSpeechNotification: SelectTarget
		AmmoPool: wormholespawner
		TargetModifiedCursor: ability2
		ConcurrentLimit: 2
		AvoidActors: true
	AmmoPool@WORMHOLESPAWNER:
		Name: wormholespawner
		Armaments: none
		Ammo: 2
	ReloadAmmoPoolCA@WORMHOLESPAWNER:
		AmmoPool: wormholespawner
		Delay: 1125
		Count: 2
		ShowSelectionBar: true
		SelectionBarColor: 9988ff
		ReloadWhenAmmoReaches: 0
	WithAmmoPipsDecoration@WORMHOLESPAWNER:
		AmmoPools: wormholespawner
		RequiresSelection: true
		Position: BottomLeft
		Margin: 4, 3
	PeriodicExplosion:
		Weapon: MothershipCharge
		LocalOffset: 0,0,256
	Targetable@FleetRecallable:
		RequiresCondition: !attacking
	-SoundOnDamageTransitionCA:
	ProductionCostMultiplier@HarbingerBonus:
		Multiplier: 90
		Prerequisites: player.harbinger

#
# ---- Husks
#

SMCV.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Colony Ship)
	RenderSprites:
		Image: smcv.destroyed

HARV.Scrin.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Harvester)
	RenderSprites:
		Image: harv.scrin.destroyed
	WithShadow:
		Offset: 43, 255, 0
		ZOffset: -256

GUNW.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Gun Walker)
	RenderSprites:
		Image: gunw.destroyed

SEEK.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Seeker)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: seek.destroyed
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129

INTL.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Interloper)
	RenderSprites:
		Image: intl.destroyed

CORR.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Corrupter)
	RenderSprites:
		Image: corr.destroyed

DEVO.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Devourer)
	RenderSprites:
		Image: devo.destroyed

STCR.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Stormcrawler)
	RenderSprites:
		Image: stcr.destroyed

RUIN.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Ruiner)
	RenderSprites:
		Image: ruin.destroyed

TPOD.Husk:
	Inherits: ^Husk
	Tooltip:
		Name: Husk (Annihilator Tripod)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: tpod.destroyed

RTPD.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Reaper Tripod)
	ThrowsParticle@turret:
		Anim: turret
	RenderSprites:
		Image: rtpd.destroyed

OBLT.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Obliterator)
	RenderSprites:
		Image: oblt.destroyed

NULL.Husk:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Husk (Nullifier)
	RenderSprites:
		Image: null.destroyed

STMR.Husk:
	Inherits: ^PlaneHusk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Stormrider
	Aircraft:
		TurnSpeed: 16
		Speed: 146
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -250,-250,100
		StartColorUsePlayerColor: true
		EndColorUsePlayerColor: true
		StartColorAlpha: 64
	Contrail@2:
		Offset: -250,250,100
		StartColorUsePlayerColor: true
		EndColorUsePlayerColor: true
		StartColorAlpha: 64
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: stmr

STMR.Husk.EMP:
	Inherits: STMR.Husk
	Inherits@EMP: ^EmpVisualEffect

TORM.Husk:
	Inherits: ^PlaneHusk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Tormentor
	Aircraft:
		TurnSpeed: 28
		Speed: 201
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -750,-200,300
		StartColorUsePlayerColor: true
		EndColorUsePlayerColor: true
	Contrail@2:
		Offset: -750,200,300
		StartColorUsePlayerColor: true
		EndColorUsePlayerColor: true
	RevealsShroud:
		Range: 11c0
		MinRange: 9c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	RenderSprites:
		Image: torm

TORM.Husk.EMP:
	Inherits: TORM.Husk
	Inherits@EMP: ^EmpVisualEffect

ENRV.Husk:
	Inherits: ^HelicopterHusk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Enervator
		GenericName: Destroyed Aircraft
	Aircraft:
		TurnSpeed: 14
		Speed: 170
	FallsToEarth:
		MaximumSpinSpeed: 20
		Velocity: 86
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		StationaryInterval: 3
		SpawnAtLastPosition: False
		TrailWhileStationary: True
		Type: CenterPosition
	Contrail@1:
		Offset: -750,-200,300
		StartColorUsePlayerColor: true
		EndColorUsePlayerColor: true
	Contrail@2:
		Offset: -750,200,300
		StartColorUsePlayerColor: true
		EndColorUsePlayerColor: true
	RevealsShroud:
		Range: 12c0
		MinRange: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 10c0
		Type: GroundPosition
	RenderSprites:
		Image: enrv

ENRV.Husk.EMP:
	Inherits: ENRV.Husk
	Inherits@EMP: ^EmpVisualEffect

DEVA.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Devastator Warship
		GenericName: Destroyed Aircraft
	Aircraft:
		TurnSpeed: 8
		Speed: 35
	FallsToEarth:
		MaximumSpinSpeed: 0
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: deva
	FallsToEarth:
		Explosion: KirovExplode
	SpawnActorOnDeath:
		Actor: DEVA.Husk.Ground

DEVA.Husk.EMP:
	Inherits: DEVA.Husk
	Inherits@EMP: ^EmpVisualEffect

DEVA.Husk.Ground:
	Inherits: ^Husk
	Inherits@SHRAPNEL: ^ThrowsShrapnelBig
	RenderSprites:
		Image: deva
	Tooltip:
		Name: Husk (Devastator Warship)
	KillsSelf:
		Delay: 1
		-RequiresCondition:
	FireWarheadsOnDeath:
		Weapon: KirovExplode
	-OwnerLostAction:
	-CaptureManager:
	-Capturable:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-GivesCashOnCaptureCA:
	-GrantConditionOnCapture:
	WithIdleOverlay@Burns:
		-RequiresCondition:
	ChangesHealth:
		-RequiresCondition:

PAC.Husk:
	Inherits: ^HelicopterHusk
	Tooltip:
		Name: Planetary Assault Carrier
		GenericName: Destroyed Aircraft
	Aircraft:
		TurnSpeed: 8
		Speed: 30
	FallsToEarth:
		MaximumSpinSpeed: 0
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: pac
	FallsToEarth:
		Explosion: KirovExplode
	SpawnActorOnDeath:
		Actor: PAC.Husk.Ground
	WithFacingSpriteBody:
		Sequence: fully-deployed

PAC.Husk.EMP:
	Inherits: PAC.Husk
	Inherits@EMP: ^EmpVisualEffect

PAC.Husk.Ground:
	Inherits: ^Husk
	Inherits@SHRAPNEL: ^ThrowsShrapnelBig
	RenderSprites:
		Image: pac
	Tooltip:
		Name: Husk (Planetary Assault Carrier)
	KillsSelf:
		Delay: 1
		-RequiresCondition:
	FireWarheadsOnDeath:
		Weapon: KirovExplode
	-OwnerLostAction:
	-CaptureManager:
	-Capturable:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-GivesCashOnCaptureCA:
	-GrantConditionOnCapture:
	WithIdleOverlay@Burns:
		-RequiresCondition:
	ChangesHealth:
		-RequiresCondition:

MSHP.Husk:
	Inherits: ^HelicopterHusk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Tooltip:
		Name: Mothership
		GenericName: Destroyed Aircraft
	Aircraft:
		TurnSpeed: 8
		Speed: 30
	FallsToEarth:
		MaximumSpinSpeed: 0
		Velocity: 35
		Explosion: MothershipExplode
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: false
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	RenderSprites:
		Image: mshp
	SpawnActorOnDeath:
		Actor: MSHP.Husk.Ground
	WithIdleOverlay@RING:
		Image: mshp
		Sequence: ring
		Offset: 0,0,0
	WithShadow:
		ZOffset: -3072

MSHP.Husk.EMP:
	Inherits: MSHP.Husk
	Inherits@EMP: ^EmpVisualEffect

MSHP.Husk.Ground:
	Inherits: ^Husk
	Inherits@ScrinUnitPalette: ^ScrinUnitPalette
	Inherits@SHRAPNEL: ^ThrowsShrapnelBig
	RenderSprites:
		Image: mshp
	Tooltip:
		Name: Husk (Mothership)
	KillsSelf:
		Delay: 1
		-RequiresCondition:
	FireWarheadsOnDeath:
		Weapon: MothershipExplode
	WithIdleOverlay@RING:
		Image: mshp
		Sequence: ring
		Offset: 0,0,0
	-OwnerLostAction:
	-CaptureManager:
	-Capturable:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-GivesCashOnCaptureCA:
	-GrantConditionOnCapture:
	WithIdleOverlay@Burns:
		-RequiresCondition:
	ChangesHealth:
		-RequiresCondition:

#
# ---- Buildings
#

SWAL:
	Inherits: ^Wall
	RenderSprites:
		Palette: scrin
	Building:
		BuildSounds: scrinbuild.aud
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 30
		Prerequisites: anypower, ~structures.scrin
		Description: • Stops units\n• Blocks enemy fire\n• Regenerates over time
		IconPalette: chromes
	Valued:
		Cost: 200
	CustomSellValue:
		Value: 0
	Tooltip:
		Name: Biometallic Wall
	SoundOnDamageTransition:
		DamagedSounds: crmble2.aud
		DestroyedSounds: kaboom30.aud
	Health:
		HP: 36000
	ChangesHealth:
		PercentageStep: 1
		Delay: 10
		StartIfBelow: 100
		DamageCooldown: 25
		RequiresCondition: !is-neutral
	GrantConditionIfOwnerIsNeutral:
		Condition: is-neutral
	Armor:
		Type: Brick
	Crushable:
		CrushClasses: heavywall
	BlocksProjectiles:
	LineBuild:
		NodeTypes: scrinwall
	LineBuildNode:
		Types: scrinwall
	WithWallSpriteBody:
		Type: scrinwall

SFAC:
	Inherits: ^ScrinBuilding
	Inherits@SHAPE: ^3x2Shape
	Inherits@BOTI: ^BotFallbackInsurance
	Inherits@BLD: ^ProducesBuildings
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 1000
		Prerequisites: ~disabled
		IconPalette: chrometd
		Description: Produces structures.
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Valued:
		Cost: 3000
	Tooltip:
		Name: Colony Platform
	BaseBuilding:
	Transforms:
		RequiresCondition: factundeploy
		PauseOnCondition: chrono-vortex || being-captured || c4 || tnt || build-incomplete || being-warped || hacked
		IntoActor: smcv
		Offset: 1,0
		Facing: 96
	TransformsIntoMobile:
		RequiresCondition: factundeploy
		Locomotor: heavywheeled
		RequiresForceMove: true
	TransformsIntoPassenger:
		RequiresCondition: factundeploy
		CargoType: Vehicle
		RequiresForceMove: true
	TransformsIntoRepairable:
		RequiresCondition: factundeploy
		RepairActors: fix, rep
		RequiresForceMove: true
	TransformsIntoTransforms:
		RequiresCondition: factundeploy && build-incomplete
	Sellable:
		RequiresCondition: !build-incomplete && !chrono-vortex && !being-captured && !c4 && !being-warped && !hacked
	GrantConditionOnPrerequisite@GLOBALFACTUNDEPLOY:
		Condition: factundeploy
		Prerequisites: global-factundeploy
	TracksCapturedFaction:
	ValidFactions:
		Factions: scrin, reaper, traveler, harbinger, collector
	ProvidesPrerequisite@scrin:
		Prerequisite: structures.scrin
	ProvidesPrerequisiteValidatedFaction@reaper:
		Factions: reaper
		Prerequisite: structures.reaper
	ProvidesPrerequisiteValidatedFaction@traveler:
		Factions: traveler
		Prerequisite: structures.traveler
	ProvidesPrerequisiteValidatedFaction@harbinger:
		Factions: harbinger
		Prerequisite: structures.harbinger
	ProvidesPrerequisiteValidatedFaction@collector:
		Factions: collector
		Prerequisite: structures.collector
	ProvidesPrerequisite@anyconyard:
		Prerequisite: anyconyard
	BaseProvider:
		Range: 16c0
		PauseOnCondition: being-captured
		RequiresCondition: !economy-policy2
	BaseProvider@EconomyPolicy2:
		Range: 20c0
		PauseOnCondition: being-captured
		RequiresCondition: economy-policy2
	GrantConditionOnPrerequisite@EconomyPolicy2:
		Condition: economy-policy2
		Prerequisites: economy.policy, influence.level2
	WithBuildingPlacedAnimation:
		RequiresCondition: !build-incomplete && !chrono-vortex
	Power:
		Amount: 0
	ProvidesPrerequisite@buildingname:
	ConyardChronoReturn:
		ReturnOriginalActorOnCondition: build-incomplete
		Condition: chrono-vortex
		Damage: 950
	TransferTimedExternalConditionOnTransform:
		Condition: invulnerability
	TransferTimedExternalConditionOnTransform@INVIS:
		Condition: invisibility
	SpawnActorsOnSellCA:
		ActorTypes: s1,s1,s1,s1,s6
		GuaranteedActorTypes: s1, s1
	-UpdatesBuildOrder:
	GrantConditionOnPrerequisite@LoyalistBonus:
		Condition: loyalist-allegiance
		Prerequisites: loyalist.allegiance
	ProximityExternalCondition@IchorBoost:
		Range: 16c0
		Condition: ichor-boost
		ValidRelationships: Neutral
		RequiresCondition: loyalist-allegiance

REAC:
	Inherits: ^ScrinBuilding
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@SHAPE: ^2x2Shape
	Inherits@POWERDRAIN: ^PowerDrainable
	Inherits@OVERLOAD: ^HackableOverloadable
	HitShape:
		TargetableOffsets: 630,299,0
	WithSpriteBody:
		PauseOnCondition: disabled
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 10
		Prerequisites: ~structures.scrin
		IconPalette: chromes
		Description: Provides power for other structures.
	Valued:
		Cost: 300
	Tooltip:
		Name: Reactor
	ProvidesPrerequisite:
		Prerequisite: anypower
	Building:
		Footprint: xX xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 40000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
	Power:
		Amount: 100
	ScalePowerWithHealth:
	ProvidesPrerequisite@buildingname:
	UpdatesBuildOrder:
		Limit: 2

REA2:
	Inherits: ^ScrinBuilding
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@SHAPE: ^3x2Shape
	Inherits@POWERDRAIN: ^PowerDrainable
	Inherits@OVERLOAD: ^HackableOverloadable
	HitShape:
		TargetableOffsets: -355,-1024,0
	WithSpriteBody:
		PauseOnCondition: disabled
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 110
		Prerequisites: anyradar, ~structures.scrin, ~techlevel.medium
		IconPalette: chrome
		Description: Provides double the power of\n  a standard Reactor.
	Valued:
		Cost: 500
	Tooltip:
		Name: Advanced Reactor
	ProvidesPrerequisite:
		Prerequisite: anypower
	Building:
		Footprint: xxx Xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 70000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 5c0
	WithBuildingBib:
	Power:
		Amount: 200
	ProvidesPrerequisite@buildingname:
	ScalePowerWithHealth:
	UpdatesBuildOrder:
		Limit: 1

PROC.SCRIN:
	Inherits: ^ScrinBuilding
	Inherits@BOTREF: ^BotRefinery
	Inherits@ResourceDrainable: ^ResourceDrainable
	Inherits@EconomyPolicyDiscount: ^EconomyPolicyDiscount
	Inherits@EconomyPolicyTimeReduction: ^EconomyPolicyTimeReduction
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -512
			BottomRight: 1536, 853
	HitShape@TOP:
		Type: Rectangle
			TopLeft: -512, -1450
			BottomRight: 896, -512
	Valued:
		Cost: 1800
	CustomSellValue:
		Value: 400
	Tooltip:
		Name: Refinery
	Buildable:
		BuildPaletteOrder: 60
		Prerequisites: anypower, ~structures.scrin
		Queue: BuildingSQ, BuildingMQ
		IconPalette: chromes
		Description: Processes raw Tiberium, Ore\n  and Gems into credits.
		BuildDuration: 1400
	Building:
		Footprint: _x_ xxx +++ ===
		Dimensions: 3,4
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 90000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: StealCreditsInfiltrate
	Refinery:
		TickRate: 15
	DockHost:
		Type: Unload
		DockAngle: 448
		DockOffset: -1c0, 1c0, 0
		IsDragRequired: True
		DragOffset: -554,512,0
		DragLength: 12
	StoresPlayerResourcesCA:
		Capacity: 2000
	WithResourceStoragePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 17
	Selectable:
		Bounds: 3072, 2512, 0, 256
		DecorationBounds: 3114, 3072
	InfiltrateForCash:
		Types: StealCreditsInfiltrate
		Percentage: 50
		InfiltrationNotification: CreditsStolen
		PlayerExperience: 15
	FreeActor:
		Actor: HARV.Scrin
		SpawnOffset: 1,2
		Facing: 64
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@anyrefinery:
		Prerequisite: anyrefinery
	CashHackable:
	UpdatesCount:
		Type: Refineries

PORT:
	Inherits: ^ScrinBuilding
	Inherits@SHAPE: ^2x2Shape
	Inherits@INF: ^ProducesInfantry
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-512,0, 355,512,0, -281,-512,0, -630,512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 20
		Prerequisites: anypower, ~structures.scrin
		IconPalette: chromes
		Description: Produces infantry.
	Valued:
		Cost: 500
	Tooltip:
		Name: Portal
	Building:
		Footprint: xx xx ++
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 0,512,0
		ExitCell: 0,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: 256,512,0
		ExitCell: 1,2
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: 0,512,0
		ExitCell: -1,2
		Priority: 0
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: 256,512,0
		ExitCell: 2,2
		Priority: 0
	ValidFactions:
		Factions: scrin, reaper, traveler, harbinger, collector
	ProvidesPrerequisite@infantryany:
		Prerequisite: infantry.any
	ProvidesPrerequisite@scrin:
		Prerequisite: infantry.scrin
	ProvidesPrerequisiteValidatedFaction@reaper:
		Factions: reaper
		Prerequisite: infantry.reaper
	ProvidesPrerequisiteValidatedFaction@traveler:
		Factions: traveler
		Prerequisite: infantry.traveler
	ProvidesPrerequisiteValidatedFaction@harbinger:
		Factions: harbinger
		Prerequisite: infantry.harbinger
	ProvidesPrerequisiteValidatedFaction@collector:
		Factions: collector
		Prerequisite: infantry.collector
	ProvidesPrerequisiteValidatedFaction@intruder:
		Factions: scrin, reaper, traveler, collector
		Prerequisite: infantry.intruder
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.port
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.port
		OwnerType: Master
	InfiltrateToCreateProxyActor@spy:
		Proxy: barracks.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	WithIdleOverlay:
		RequiresCondition: !build-incomplete

WSPH:
	Inherits: ^ScrinBuilding
	Inherits@SHAPE: ^3x2Shape
	Inherits@NOMCV: ^UnlocksMcvIfNoneOwned
	Inherits@VEH: ^ProducesVehicles
	Selectable:
		Bounds: 3072, 2048
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 80
		Prerequisites: proc.scrin, ~structures.scrin, ~techlevel.low
		IconPalette: chromes
		Description: Produces vehicles.
	Valued:
		Cost: 2000
	Tooltip:
		Name: Warp Sphere
	Building:
		Footprint: xxx xxx +++
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	TracksCapturedFaction:
	ValidFactions:
		Factions: scrin ,reaper, traveler, harbinger, collector
	ProvidesPrerequisite@any:
		Prerequisite: vehicles.any
	ProvidesPrerequisite@scrin:
		Prerequisite: vehicles.scrin
	ProvidesPrerequisiteValidatedFaction@reaper:
		Factions: reaper
		Prerequisite: vehicles.reaper
	ProvidesPrerequisiteValidatedFaction@traveler:
		Factions: traveler
		Prerequisite: vehicles.traveler
	ProvidesPrerequisiteValidatedFaction@harbinger:
		Factions: harbinger
		Prerequisite: vehicles.harbinger
	ProvidesPrerequisiteValidatedFaction@collector:
		Factions: collector
		Prerequisite: vehicles.collector
	ProvidesPrerequisiteValidatedFaction@seek:
		Factions: scrin, reaper, harbinger, collector
		Prerequisite: vehicles.seek
	ProvidesPrerequisiteValidatedFaction@corr:
		Factions: scrin, reaper, traveler, harbinger
		Prerequisite: vehicles.corr
	ProvidesPrerequisiteValidatedFaction@tpod:
		Factions: scrin, traveler, harbinger, collector
		Prerequisite: vehicles.tpod
	WithBuildingBib:
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 0,0,0
		ExitCell: 0,2
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	InfiltrateToCreateProxyActor@spy:
		Proxy: vehicles.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.wsph
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.wsph
		OwnerType: Master
	WithIdleOverlay:
		RequiresCondition: !build-incomplete
	GrantExternalConditionToProduced@DRONEEXIT:
		Condition: radarenabled
		Duration: 100

NERV:
	Inherits: ^ScrinBuilding
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@UPG: ^ProducesUpgrades
	Inherits@SHAPE: ^2x2Shape
	Inherits@RESOURCESCANPOWER: ^ResourceScanPower
	Inherits@STORMSPIKEPOWER: ^StormSpikePower
	Inherits@BUZZERSWARMPOWER: ^BuzzerSwarmPower
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ProductionOptimizer1: ^ProductionOptimizer1
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 90
		Prerequisites: proc.scrin, ~structures.scrin, ~techlevel.medium
		Description: Provides an overview of the battlefield.
		IconPalette: chromes
	TooltipExtras:
		Attributes: • Requires power to operate\n• Detects nearby enemy vehicles, aircraft and structures in fog of war
	Valued:
		Cost: 1800
	Tooltip:
		Name: Nerve Center
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetShroudInfiltrate
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	ProvidesRadar:
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	RangedGpsRadarProvider:
		Range: 12c0
		TargetTypes: Vehicle, Air, Structure
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	WithRangeCircle:
		Type: RadarDetection
		Range: 12c0
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	WithBuildingBib:
	InfiltrateForExploration:
		Types: ResetShroudInfiltrate
		PlayerExperience: 15
	Power:
		Amount: -40
	ValidFactions:
		Factions: scrin, reaper, traveler, harbinger, collector
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@anyradar:
		Prerequisite: anyradar
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@radarorrepair:
		Prerequisite: radarorrepair
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@RadarOrAircraft:
		Prerequisite: radaroraircraft
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@FlameTowerOrRadar:
		Prerequisite: fturorradar
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@scrinrad:
		Prerequisite: radar.scrin
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@reaprad:
		Factions: reaper
		Prerequisite: radar.reaper
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@travrad:
		Factions: traveler
		Prerequisite: radar.traveler
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@harbrad:
		Factions: harbinger
		Prerequisite: radar.harbinger
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@desprad:
		Factions: collector
		Prerequisite: radar.collector
		RequiresCondition: !tech-locked
	ExternalCondition@JAMMED:
		Condition: jammed
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active
		RequiresCondition: !jammed && !disabled && !being-warped

SCRT:
	Inherits: ^ScrinBuilding
	Inherits@IDISABLE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^2x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@ICHORSEEDPOWER: ^IchorSeedPower
	Inherits@FORCESHIELDPOWER: ^ForceShieldPower
	Inherits@IONSURGEPOWER: ^IonSurgePower
	Inherits@GREATERCOALESCENCEPOWER: ^GreaterCoalescencePower
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ProductionOptimizer2: ^ProductionOptimizer2
	Inherits@OverlordsWrath: ^OverlordsWrathPower
	Inherits@GatewayPower: ^GatewayPower
	Inherits@AnathemaPower: ^AnathemaPower
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 150
		Prerequisites: vehicles.any, anyradar, ~structures.scrin, ~techlevel.high
		Description: Provides Scrin advanced technologies.
		IconPalette: chromes
	Valued:
		Cost: 1800
	Tooltip:
		Name: Scrin Tech Center
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Selectable:
		Bounds: 2048, 2560
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
	Power:
		Amount: -150
	ValidFactions:
		Factions: scrin, reaper, traveler, harbinger, collector
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite:
		Prerequisite: techcenter.any
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@harbscrt:
		Factions: harbinger
		Prerequisite: scrt.harbinger
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@collscrt:
		Factions: collector
		Prerequisite: scrt.collector
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@BotAllegiance:
		Prerequisite: scrin.allegiances.available
		RequiresPrerequisites: botplayer

GRAV:
	Inherits: ^ScrinBuilding
	Inherits@SHAPE: ^3x2Shape
	Inherits@AIR: ^ProducesHelicopters
	Inherits@AIRCRAFTREPAIR: ^RepairsAircraftWithRepairBay
	HitShape:
		TargetableOffsets: -355,-1024,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 130
		Prerequisites: radarorrepair, ~structures.scrin, ~techlevel.medium
		Description: Produces Scrin aircraft.
		IconPalette: chromes
	Valued:
		Cost: 500
	Tooltip:
		Name: Gravity Stabilizer
	Selectable:
		Bounds: 3072, 2560, 0, -128
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		ExitCell: 1,1
		Facing: 720
		SpawnOffset: -128,0,0
	Reservable:
	RallyPoint:
	ValidFactions:
		Factions: scrin, reaper, traveler, harbinger, collector
	ProvidesPrerequisiteValidatedFaction@traveler:
		Factions: traveler
		Prerequisite: aircraft.traveler
	ProvidesPrerequisite@scrin:
		Prerequisite: aircraft.scrin
	ProvidesPrerequisite@RadarOrAircraft:
		Prerequisite: radaroraircraft
	SupportPowerChargeBar:
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
	WithBuildingBib:
	WithRestartableIdleOverlay@orb:
		RequiresCondition: !build-incomplete && !resupplying
		RestartSequence: idle-overlay-activate
		StartSequence: idle-overlay
	WithRestartableIdleOverlay@orboff:
		RequiresCondition: !build-incomplete && resupplying
		Sequence: idle-overlay-deactivate
		PlayOnce: true
	WithIdleOverlay@pylon:
		Sequence: idle-pylon
		RequiresCondition: !build-incomplete && !damaged
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Heavy, Critical
	GrantConditionOnResupplying@RESUPPLY:
		Condition: resupplying
	Armament@UNDAMAGED:
		Weapon: GravityStabilizerZap
		LocalOffset: -800,-500,1152, -250,450,1152, 150,-500,1152
		TargetRelationships: Ally
		ForceTargetRelationships: None
		MuzzleSequence: muzzle
		MuzzlePalette: playerscrin
		RequiresCondition: !damaged
	Armament@DAMAGED:
		Weapon: GravityStabilizerZap
		LocalOffset: -250,650,1024, 150,-600,1152
		TargetRelationships: Ally
		ForceTargetRelationships: None
		MuzzleSequence: muzzle
		MuzzlePalette: playerscrin
		RequiresCondition: damaged
	WithMuzzleOverlayCA:
		IsPlayerPalette: true
	AttackOmni:
		PauseOnCondition: !resupplying
	AutoTarget:
	AutoTargetPriority@DEFAULT:
		ValidRelationships: Ally
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.grav
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.grav
		OwnerType: Master
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: StealTechInfiltrate

SIGN:
	Inherits: ^ScrinBuilding
	Inherits@POWER_OUTAGE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@FLEETRECALL: ^FleetRecallPower
	Selectable:
		Bounds: 3072, 2048
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 130000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 200
		Prerequisites: ~structures.scrin, scrt, ~techlevel.high
		Description: Provides access to the most powerful\n  Scrin units and technology.
		BuildLimit: 1
		IconPalette: chromes
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cannot be captured or hacked\n• Boosts speed of fleet ships by 10%
	-Capturable:
	-CaptureNotification:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-CaptureManager:
	Valued:
		Cost: 1800
	Tooltip:
		Name: Signal Transmitter
	Power:
		Amount: -150
	ProvidesPrerequisite@Name:
		RequiresCondition: !tech-locked
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped
	SpawnActorOnDeath:
		RequiresCondition: !being-warped
	WithDecoration@TechLock:
		RequiresCondition: tech-locked
	-MindControllable@HACKABLE:
	-Targetable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:

SREP:
	Inherits: ^ScrinBuilding
	Inherits@ProductionOptimizer1: ^ProductionOptimizer1
	HitShape:
		TargetableOffsets: 840,0,0, 598,-640,0, 598,640,0, -1060,0,0, -768,-640,0, -768,640,0
		Type: Polygon
			Points: -1536,-300, -640,-811, 640,-811, 1536,-300, 1536,555, 640,1110, -640,1110, -1536,555
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 100
		Prerequisites: vehicles.scrin, ~structures.scrin, ~techlevel.low
		Description: Repairs vehicles and aircraft.
		IconPalette: chromes
	TooltipExtras:
		Attributes: • Nearby helipads/airfields will repair landed aircraft\n• Can retrofit existing units with upgrades
	Valued:
		Cost: 1000
	Tooltip:
		Name: Regeneration Bay
	Building:
		Footprint: _+_ +++ _+_
		Dimensions: 3,3
	Selectable:
		Bounds: 2901, 1450, 0, 128
		DecorationBounds: 3072, 2048
	Health:
		HP: 80000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: true
	Reservable:
	RallyPoint:
	RepairsUnits:
		ValuePercentage: 0
		HpPerStep: 1000
		Interval: 7
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: Repairing.
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: Unit repaired.
		RequiresCondition: !forceshield && !invulnerability && !being-warped
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	ProximityExternalCondition@UNITSELL:
		Condition: unit-sellable
		Range: 1c0
	GrantConditionOnResupplying@Resupplying:
		Condition: resupplying
	Sellable:
		RequiresCondition: !resupplying && !build-incomplete && !c4 && !being-captured && !being-warped && !hacked
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@repair:
		Prerequisite: repair
	ProvidesPrerequisite@radarorrepair:
		Prerequisite: radarorrepair
	ProvidesPrerequisite@allowsmcv:
		Prerequisite: vehicles.mcv
	ProximityExternalCondition@AIRCRAFTREPAIR:
		Condition: aircraft-repair
		Range: 10c0
	WithRangeCircle@AIRCRAFTREPAIR:
		Type: AircraftRepair
		Color: FFD000AA
		Range: 10c0

SILO.SCRIN:
	Inherits: ^ScrinBuilding
	Valued:
		Cost: 150
	Tooltip:
		Name: Silo
	Buildable:
		BuildPaletteOrder: 35
		Prerequisites: proc.scrin, ~structures.scrin
		Queue: DefenseSQ, DefenseMQ
		IconPalette: chromes
		Description: Stores processed Tiberium, Ore and Gems
	-GivesBuildableArea:
	Health:
		HP: 30000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: true
	-WithSpriteBody:
	WithResourceLevelSpriteBody:
		Sequence: stages
	StoresPlayerResourcesCA:
		Capacity: 3000
	WithResourceStoragePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 5
	-MustBeDestroyed:
	-SpawnActorsOnSellCA:
	Power:
		Amount: -10
	Selectable:
		Bounds: 1024, 1024
	FireWarheadsOnDeath:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	-UpdatesBuildOrder:

MANI:
	Inherits: ^ScrinBuilding
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x2Shape
	Inherits@SUPPRESSIONPOWER: ^SuppressionPower
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 130
		Prerequisites: scrt, ~structures.scrin, ~techlevel.high
		BuildLimit: 1
		IconPalette: chrome
		Description: Slows enemy unit movement and rate of fire.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: Suppression Field
	Valued:
		Cost: 1500
	Tooltip:
		Name: Field Manipulator
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
	SupportPowerChargeBar:
	Power:
		Amount: -200
	MustBeDestroyed:
		RequiredForShortGame: false
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	ValidFactions:
		Factions: scrin, reaper, traveler, harbinger, collector
	ProductionCostMultiplier@COLLECTOR:
		Multiplier: 80
		Prerequisites: player.collector

RFGN:
	Inherits: ^ScrinBuilding
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x2Shape
	Inherits@RIFTPOWER: ^RiftPower
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 140
		Prerequisites: scrt, ~structures.scrin, ~techlevel.unrestricted
		BuildLimit: 1
		Description: Provides Rift support power.
		IconPalette: chrome
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: Rift
	Valued:
		Cost: 2500
	Tooltip:
		Name: Rift Generator
	Building:
		Footprint: xx xx
		Dimensions: 2,2
		LocalCenterOffset: 0,-512,0
	HitShape:
		Type: Rectangle
			TopLeft: -1024, -768
			BottomRight: 1024, 1536
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithIdleOverlay@ACTIVE:
		Sequence: active-overlay
		Palette: scrin
		RequiresCondition: active
	ExternalCondition@ACTIVE:
		Condition: active
	SupportPowerChargeBar:
	Power:
		Amount: -200
	ProvidesPrerequisite@buildingname:
	MustBeDestroyed:
		RequiredForShortGame: false
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	WithBuildingBib:
		HasMinibib: true

PTUR:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetGround
	RenderSprites:
		PlayerPalette: playerscrin
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 60
		Prerequisites: port, ~structures.scrin, ~techlevel.low
		Description: Anti-infantry base defense.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Can detect cloaked units
	Valued:
		Cost: 725
	CustomSellValue:
		Value: 250
	Tooltip:
		Name: Plasma Cannon
	Selectable:
		DecorationBounds: 1024, 1194, 0, -256
	Building:
	Health:
		HP: 46000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 48
		InitialFacing: 176
		RealignDelay: -1
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: !build-incomplete
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
	Armament:
		Weapon: PlasmaTurretGun
		LocalOffset: 450,70,500, 450,-70,500
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	AttackTurreted:
		PauseOnCondition: build-incomplete || empdisable || being-warped
	WithMuzzleOverlay:
	Power:
		Amount: -15
	ClassicFacingBodyOrientation:
	SpawnActorOnDeath:
		Actor: s1
	-SpawnRandomActorOnDeath:
	DetectCloaked:
		Range: 5c0

SHAR:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetAirICBM
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@AntiAirDefense: ^AntiAirDefense
	RenderSprites:
		PlayerPalette: playerscrin
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 90
		Prerequisites: anyradar, ~structures.scrin, ~techlevel.medium
		Description: Anti-aircraft base defense.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Aircraft
		Weaknesses: • Cannot attack ground units
		Attributes: • Requires power to operate\n• Can detect cloaked aircraft
	Valued:
		Cost: 800
	Tooltip:
		Name: Shard Launcher
	Selectable:
		DecorationBounds: 1024, 1877, 0, -469
	Health:
		HP: 50000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 60
		InitialFacing: 192
		RealignDelay: -1
		RequiresCondition: !build-incomplete
		PauseOnCondition: disabled || empdisable || being-warped
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
	Armament@PRIMARY:
		Weapon: ShardLauncher
		LocalOffset: 300,0,1050
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
	AttackTurreted:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	WithMuzzleOverlay:
	RenderRangeCircle:
		RangeCircleType: DefenseRangeAA
	Power:
		Amount: -40
	ClassicFacingBodyOrientation:
	DetectCloaked:
		Range: 7c0
		DetectionTypes: AirCloak
		RequiresCondition: !(disabled || empdisable || being-warped)
	SpawnActorOnDeath:
		Actor: s1
	-SpawnRandomActorOnDeath:

SCOL:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetGround
	Inherits@IONCONDUITS: ^IonConduits
	RenderSprites:
		PlayerPalette: playerscrin
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 80
		Prerequisites: anyradar, ~structures.scrin, ~techlevel.medium
		Description: Advanced base defense.
		IconPalette: chrome
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Requires power to operate\n• Can detect cloaked units
	Valued:
		Cost: 1400
	Tooltip:
		Name: Storm Column
	Selectable:
		DecorationBounds: 1280, 2048, 0, -597
	Health:
		HP: 45000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: false
	HitShape:
		UseTargetableCellsOffsets: true
		TargetableOffsets: 0,0,768
	RevealsShroud@GAPGEN:
		Range: 6c0
	WithBuildingBib:
		HasMinibib: true
	Armament:
		Weapon: StormColumnZap
		LocalOffset: 0,0,900, 0,0,1100, 0,0,700, 0,0,1300, 0,0,500
	AttackOmni:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	Power:
		Amount: -85
	DetectCloaked:
		RequiresCondition: !(disabled || empdisable || being-warped)
	SpawnActorOnDeath:
		Actor: s1
	-SpawnRandomActorOnDeath:
	GrantConditionOnAttackCA@IONSTORM:
		RequiresCondition: ioncon-upgrade && !(disabled || empdisable || being-warped)
	GrantConditionOnDamage@IONSTORM:
		RequiresCondition: ioncon-upgrade && !(disabled || empdisable || being-warped)

STRM:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	RenderSprites:
		Palette: ioncloud
	HiddenUnderFog:
		Type: CenterPosition
	Tooltip:
		Name: Ion Storm
	# required for death animation
	Health:
		HP: 1
	HitShape:
	KillsSelf:
		RemoveInstead: False
		Delay: 175, 225
	Wanders:
		WanderMoveRadius: 1
		ReduceMoveRadiusDelay: 3
	WithSpriteBody:
		RequiresCondition: strm1
		StartSequence: make
	WithDeathAnimation:
		RequiresCondition: strm1
		DeathPaletteIsPlayerPalette: False
		UseDeathTypeSuffix: False
		CrushedSequence: die
		FallbackSequence: die
		DeathSequencePalette: ioncloud
	WithSpriteBody@Cloud2:
		RequiresCondition: strm2
		StartSequence: make2
		Sequence: idle2
		Name: body2
	WithDeathAnimation@Cloud2:
		DeathSequence: die2
		RequiresCondition: strm2
		DeathPaletteIsPlayerPalette: False
		UseDeathTypeSuffix: False
		CrushedSequence: die2
		FallbackSequence: die2
		DeathSequencePalette: ioncloud
	WithSpriteBody@Cloud3:
		RequiresCondition: strm3
		StartSequence: make3
		Sequence: idle3
		Name: body3
	WithDeathAnimation@Cloud3:
		DeathSequence: die3
		DeathPaletteIsPlayerPalette: False
		UseDeathTypeSuffix: False
		CrushedSequence: die3
		FallbackSequence: die3
		RequiresCondition: strm3
		DeathSequencePalette: ioncloud
	WithSpriteBody@Cloud4:
		RequiresCondition: strm4
		StartSequence: make4
		Sequence: idle4
		Name: body4
	WithDeathAnimation@Cloud4:
		DeathSequence: die4
		DeathPaletteIsPlayerPalette: False
		UseDeathTypeSuffix: False
		CrushedSequence: die4
		FallbackSequence: die4
		RequiresCondition: strm4
		DeathSequencePalette: ioncloud
	Mobile:
		Locomotor: cloud
		Speed: 3
	Hovers:
		Ticks: 12
	Interactable:
	GrantRandomCondition@CloudShape:
		Conditions: strm1, strm2, strm3, strm4

STRM.Live:
	Inherits: STRM
	Inherits@AUTOTARGET: ^AutoTargetAll
	RenderSprites:
		Image: strm
	JamsMissiles@IONSTORM:
		Chance: 10
		Range: 5c0
	AutoTarget:
		InitialStance: AttackAnything
	Armament:
		Weapon: IonCloudZap
		LocalOffset: 0,0,0
	AttackTurreted:
		RequiresCondition: live
	Turreted:
		Offset: 0, 0, 3072
	GrantDelayedCondition:
		Condition: live
		Delay: 25, 75
	PeriodicExplosion:
		InitialDelay: 20
		Weapon: IonCloudChargeSource
		LocalOffset: 0, 0, 3072

STRM2:
	Inherits: STRM.Live
	RenderSprites:
		Image: strm2
	Armament:
		Weapon: IonCloudMinorZap
	Turreted:
		Offset: 0,0, 1024
	PeriodicExplosion:
		LocalOffset: 0, 0, 1024
	JamsMissiles@IONSTORM:
		Range: 2c512

SCOL.Temp:
	Inherits: SCOL
	Tooltip:
		Name: Storm Spike
	RenderSprites:
		Image: scol
	-Sellable:
	-Buildable:
	Health:
		HP: 75000
	KillsSelf:
		Delay: 750
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
	RevealsShroud@GAPGEN:
		Range: 4c0
	-Power:
	-SpawnActorOnDeath:
	-WithBuildingRepairDecoration:
	-RepairableBuilding:
	-InstantlyRepairable:
	-GrantConditionOnPowerState@LOWPOWER:
	-WithColoredOverlay@IDISABLE:
	-GrantCondition@IDISABLE:
	-PowerMultiplier@TEMPORAL:
	-PowerMultiplier@POWERDOWN:
	-WithDecoration@POWERDOWN:
	-RevealsShroudMultiplier@POWERDOWN:
	-ToggleConditionOnOrder:
	-Armament:
	Armament:
		Weapon: StormColumnZap
		LocalOffset: 0,0,900, 0,0,1100, 0,0,700, 0,0,1300, 0,0,500
	AttackOmni:
		PauseOnCondition: build-incomplete || empdisable || being-warped
	DetectCloaked:
		RequiresCondition: !(empdisable || being-warped)
	GrantConditionOnAttackCA@IONSTORM:
		RequiresCondition: ioncon-upgrade && !(empdisable || being-warped)
	GrantConditionOnDamage@IONSTORM:
		RequiresCondition: ioncon-upgrade && !(empdisable || being-warped)
	-MapEditorData:
	-ChangesHealth@DefensePolicy2:
	-UpdatesKillCount@BuildingsOrHarvesters:

VSPK:
	Inherits: ^BasicBuilding
	-ShakeOnDeath:
	OwnerLostAction:
		Action: Kill
	RenderSprites:
		PlayerPalette: playerscrin
	FireWarheadsOnDeath:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	Selectable:
		Bounds: 1280, 1792, 0, -512
		DecorationBounds: 1280, 2560, 0, -768
	Health:
		HP: 40000
	Tooltip:
		Name: Voidspike
	TooltipExtras:
		Attributes: • Corrupts resources\n• Damages nearby enemies over time\n• Direct damage is reflected back to the attacker
		Description: Gradually transforms nearby resources into Black Tiberium,\n  which is devoid of most of its useful properties.
	Armor:
		Type: Concrete
	Targetable:
		TargetTypes: Ground, Structure, Defense
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Building:
		Dimensions: 1,1
		Footprint: x
		TerrainTypes: Clear,Road,Ore,Gems,Tiberium,BlueTiberium,BlackTiberium
		AllowPlacementOnResources: true
	WithMakeAnimation:
	SoundOnDamageTransition:
		-DamagedSounds:
		DestroyedSounds: kaboom15.aud
	ProximityExternalCondition@VoidSpike:
		Condition: voidspike
		Range: 8c0
		ValidRelationships: Enemy, Neutral
	WithRangeCircleCA@VoidSpike:
		Type: VoidSpike
		Range: 8c0
		ValidRelationships: Ally, Enemy, Neutral
		UsePlayerColor: true
		PlayerColorAlpha: 160
	ConvertsResources:
		Range: 8c0
		ConvertFrom: Tiberium, BlueTiberium, Ore, Gems
		ConvertTo: BlackTiberium
		Interval: 10
		Amount: 1
	PeriodicExplosion:
		Weapon: VoidSpike
	AmbientSoundCA:
		SoundFiles: grcl-loop1.aud, grcl-loop2.aud
		Delay: 0
		Interval: 20
		VolumeMultiplier: 0.3
	WithIdleOverlay:
		Sequence: overlay
		Palette: scrin
		IsDecoration: true
	ReflectsDamage:
		DamagePercentage: 50
		ValidRelationships: Ally, Enemy, Neutral
		ReflectToAttacker: true

#
# ---- misc
#

BUZZ:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@selection: ^SelectableCombatUnit
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	HiddenUnderFog:
		Type: CenterPosition
	Tooltip:
		Name: Buzzer Swarm
	TooltipExtras:
		Description: Swarm of biometallic shards.
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
		Attributes: • Untargetable\n• Blinds enemies
	RevealsShroud:
		MinRange: 1c0
		Range: 6c0
		RevealGeneratedShroud: False
		Type: CenterPosition
	RevealsShroud@GAPGEN:
		Range: 1c0
		Type: CenterPosition
	PeriodicExplosion:
		Weapon: BuzzerVortex
	WithSpriteBody:
	HitShape:
	KillsSelf:
		Delay: 450
	# required for death animation
	Health:
		HP: 1
	Aircraft:
		CanHover: True
		Speed: 33
		CruiseAltitude: 1
		LandableTerrainTypes: Clear,Road,Rough,Ore,Gems,Tiberium,BlueTiberium,BlackTiberium,Water,Tree,River,Rock,Beach,Bridge,Tunnel,Wall,Ford
	AmbientSound:
		SoundFiles: buzzers-idle1.aud, buzzers-idle2.aud, buzzers-idle3.aud
		Interval: 10
	Voiced:
		VoiceSet: BuzzersVoice
	Armament@PRIMARY:
		Weapon: BuzzerTargeting
		RequiresCondition: alive
	AttackFrontal:
		FacingTolerance: 0
	WithColoredSelectionBox@PLAYERBOX:
		ColorSource: Player
		ValidRelationships: Ally, Enemy
	WithMakeAnimation:
	WithDeathAnimation:
		UseDeathTypeSuffix: false
		CrushedSequence: die
		FallbackSequence: die
		DeathSequencePalette: scrin
		DeathPaletteIsPlayerPalette: false
	GrantTimedCondition:
		Duration: 450
		Condition: alive
	TimedConditionBar@ALIVE:
		Condition: alive
		Color: 777777
	ProximityExternalCondition@Blind:
		MaximumVerticalOffset: 512
		Range: 1c256
		Condition: blinded
		ValidRelationships: Enemy, Neutral
	Targetable:
		TargetTypes: BuzzerSwarm

BUZZ.AI:
	Inherits: BUZZ
	RenderSprites:
		Image: buzz
	AttackWander:
		WanderMoveRadius: 1
		ReduceMoveRadiusDelay: 3

MSPK:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@selection: ^SelectableSupportUnit
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Tooltip:
		Name: Mind Spark
	RenderSprites:
		Palette: caneon
	WithSpriteBody:
	ClassicFacingBodyOrientation:
		QuantizedFacings: 1
	HiddenUnderFog:
		Type: CenterPosition
	Selectable:
		Bounds: 896, 896
		DecorationBounds: 896, 896
	HitShape:
	RevealsShroud:
		Range: 5c0
		Type: CenterPosition
	Armament@PRIMARY:
		Weapon: MindSparkZap
	AttackTurreted:
	Turreted:
	Aircraft:
		CanHover: True
		Speed: 46
		CruiseAltitude: 1
		LandableTerrainTypes: Clear,Road,Rough,Ore,Gems,Tiberium,BlueTiberium,BlackTiberium,Water,Tree,River,Rock,Beach,Bridge,Tunnel,Wall,Ford
	KillsSelf:
		Delay: 325
	WithDeathAnimation:
		UseDeathTypeSuffix: false
		CrushedSequence: die
		FallbackSequence: die
		DeathSequencePalette: caneon
		DeathPaletteIsPlayerPalette: false
	Health:
		HP: 1
	Voiced:
		VoiceSet: MindSparkVoice
	WithColoredSelectionBox@PLAYERBOX:
		ColorSource: Player
		ValidRelationships: Ally, Enemy
	Targetable:
		TargetTypes: MindSpark
	PeriodicExplosion:
		Weapon: MindSparkEruption
		LocalOffset: 0,0,128

GRCL:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@selection: ^SelectableCombatUnit
	HiddenUnderFog:
		Type: CenterPosition
	Tooltip:
		Name: Greater Coalescence
	TooltipExtras:
		Description: Pulsing biomass that heals allies and feeds on enemies.
		Attributes: • Heals nearby allies\n• Slows and drains health from enemies\n• Disables power plants
	RenderSprites:
		Palette: caneon
	WithMakeAnimation:
	RevealsShroud:
		MinRange: 1c0
		Range: 6c0
		RevealGeneratedShroud: False
		Type: CenterPosition
	RevealsShroud@GAPGEN:
		Range: 1c0
		Type: CenterPosition
	Targetable:
		TargetTypes: Ground, Vehicle
	Health:
		HP: 64000
	Armor:
		Type: Light
	PeriodicExplosion:
		Weapon: GreaterCoalescence
	WithSpriteBody:
	HitShape:
	Aircraft:
		CanHover: True
		Speed: 33
		CruiseAltitude: 1
		LandableTerrainTypes: Clear,Road,Rough,Ore,Gems,Tiberium,BlueTiberium,BlackTiberium,Water,Tree,River,Rock,Beach,Bridge,Tunnel,Wall,Ford
	Voiced:
		VoiceSet: GiantScrinVoice
	WithColoredSelectionBox@PLAYERBOX:
		ColorSource: Player
		ValidRelationships: Ally, Enemy
	FireWarheadsOnDeath:
		Weapon: LeecherExplode
		EmptyWeapon: LeecherExplode
	ConvertsDamageToHealth:
		DamagePercentConverted: 200
	ChangesHealth:
		Step: -2000
		StartIfBelow: 101
		Delay: 25
		RequiresCondition: !leeching
	WithRangeCircleCA:
		Type: GreaterCoalescence
		Range: 4c512
		UsePlayerColor: true
		PlayerColorAlpha: 192
		Visible: Always
	ProximityExternalCondition@HEAL:
		Condition: lchr-healing
		Range: 4c512
	GrantConditionOnHealingReceived:
		Condition: leeching
		MinimumHealing: 100
		StackDuration: 25
	AmbientSoundCA:
		SoundFiles: grcl-loop1.aud, grcl-loop2.aud
		Delay: 0
		Interval: 20
		VolumeMultiplier: 0.8
		RequiresCondition: leeching
	MapEditorData:
		Categories: Critter
	WithShadow:
		Offset: 80, 160, 0
		ZOffset: -161
		ShadowColor: 11000070

TBCL:
	Inherits: GRCL
	Tooltip:
		Name: Tiberium Coalescence
	TooltipExtras:
		Description: Pulsing biomass that damages nearby units.
		-Attributes:
	PeriodicExplosion:
		Weapon: TiberiumCoalescence
	FireWarheadsOnDeath:
		Weapon: CorrupterExplode
		EmptyWeapon: CorrupterExplode
	-GrantConditionOnHealingReceived:
	-WithColoredSelectionBox@PLAYERBOX:
	-ProximityExternalCondition@HEAL:
	-ConvertsDamageToHealth:
	-ChangesHealth:
	-WithRangeCircleCA:
	Health:
		HP: 200000
	AttackFrontal:
		FacingTolerance: 0
	Targetable@TBCL:
		TargetTypes: TiberiumCoalescence
	-AmbientSoundCA:
	WithShadow:
		ShadowColor: 00110070
	GpsRadarDot:
		Sequence: LargeInfantry
	ChangesHealth:
		PercentageStep: 1
		Delay: 50
		StartIfBelow: 101
		DamageCooldown: 0

^RiftBase:
	Interactable:
		Bounds: 64, 64
	ScriptTriggers:
	RenderSprites:
	WithSpriteBody:
	ClassicFacingBodyOrientation:
		QuantizedFacings: 1
	HiddenUnderFog:
		Type: GroundPosition
	HitShape:
	Immobile:
		OccupiesSpace: false

Rift:
	Inherits: ^RiftBase
	EditorOnlyTooltip:
		Name: (Rift)
	RenderSprites:
		Image: riftanim
		Palette: effect-ignore-lighting-alpha85
	PeriodicExplosion:
		Weapon: Rift
	KillsSelf:
		Delay: 360
	# required for spawning actor on death
	Health:
		HP: 1
	WithSpriteBody:
		StartSequence: spawn
	AmbientSound:
		SoundFiles: rift1.aud, rift2.aud, rift3.aud
		Interval: 50
	SpawnActorOnDeath:
		Actor: riftfade
		SkipMakeAnimations: false
	RevealsShroud:
		Range: 10c0
		Type: CenterPosition

RiftFade:
	Inherits: Rift
	WithSpriteBody:
		Sequence: dead
	WithMakeAnimation:
		Sequence: fade
	KillsSelf:
		Delay: 24
	-SpawnActorOnDeath:
	-PeriodicExplosion:
	-AmbientSound:

RiftMinor:
	Inherits: ^RiftBase
	EditorOnlyTooltip:
		Name: (Minor Rift)
	RenderSprites:
		Image: riftanimmd
	PeriodicExplosion:
		Weapon: MiniRift
	KillsSelf:
		Delay: 120

camera.resourcescan:
	Inherits: ^CameraBase
	RevealsShroud:
		Range: 5c0

IchorSeeder:
	Interactable:
	ScriptTriggers:
	Immobile:
		OccupiesSpace: false
	BodyOrientation:
		QuantizedFacings: 1
	WithSpriteBody:
	HitShape:
	HiddenUnderFog:
		Type: GroundPosition
	RevealsShroud:
		Range: 4c0
		Type: CenterPosition
	SeedsResource:
		ResourceType: Tiberium
		Interval: 2
	RenderSprites:
		Image: ichorseed
		Palette: scrin
	WithDeathAnimation:
		UseDeathTypeSuffix: False
		CrushedSequence: fade
		FallbackSequence: fade
		DeathSequencePalette: playerscrin
	KillsSelf:
		Delay: 225

IonSurge:
	Interactable:
		Bounds: 64, 64
	ScriptTriggers:
	BodyOrientation:
		QuantizedFacings: 1
	WithSpriteBody:
	WithMakeAnimation:
	HitShape:
	HiddenUnderFog:
		Type: GroundPosition
	Immobile:
		OccupiesSpace: false
	RenderSprites:
		Image: ionsurge
		Palette: scrin
	PeriodicExplosion:
		Weapon: IonSurge
	KillsSelf:
		Delay: 200
	# required for spawning actor on death
	Health:
		HP: 1
	AmbientSound:
		SoundFiles: ionsurgeloop.aud
		Interval: 50
	WithRangeCircleCA@IonSurge:
		Type: IonSurge
		Range: 5c0
		Visible: Always
		ValidRelationships: Ally
		UsePlayerColor: true
		PlayerColorAlpha: 192
	SpawnActorOnDeath:
		Actor: ionsurgefade
		SkipMakeAnimations: false

IonSurgeFade:
	Inherits: IonSurge
	WithSpriteBody:
		Sequence: dead
	WithMakeAnimation:
		Sequence: fade
	KillsSelf:
		Delay: 24
	-SpawnActorOnDeath:
	-PeriodicExplosion:
	-AmbientSound:
	-WithRangeCircleCA@IonSurge:

^WormholeBase:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@shape: ^1x1Shape
	Tooltip:
		Name: Wormhole
	RenderSprites:
		PlayerPalette: playerscrin
	WithSpriteBody:
	ClassicFacingBodyOrientation:
		QuantizedFacings: 1
	HiddenUnderFog:
		Type: CenterPosition
	Immobile:
		OccupiesSpace: true
	Interactable:

WORMHOLE:
	Inherits: ^WormholeBase
	Inherits@3: ^SelectableBuilding
	-Interactable:
	Selectable:
		Bounds: 1365, 1365
	HitShape:
	Targetable:
		TargetTypes: Ground, Structure, Defense, Wormhole
	Health:
		HP: 50000
	Armor:
		Type: Concrete
	RevealsShroud:
		Range: 4c0
		Type: CenterPosition
	TeleportNetwork:
		Type: Wormhole
		Delay: 0
	RallyPoint:
		Path: 1,1
	Exit:
	MapEditorData:
		Categories: System
	WithDeathAnimation:
		UseDeathTypeSuffix: false
		CrushedSequence: die
		FallbackSequence: die
	WithMakeAnimation:

REBELGATEWAY:
	Inherits: ^WormholeBase
	Inherits@SelectableBuilding: ^SelectableBuilding
	-Interactable:
	Tooltip:
		Name: Gateway
	TooltipExtras:
		Attributes: • Can be powered down to disable, or sold to remove.
		Description: Acts as a remote exit for a targeted production structure.
	RenderSprites:
		Image: wormholelg
	Selectable:
		Bounds: 2048, 2048
	RevealsShroud:
		Range: 4c0
		Type: CenterPosition
	HitShape:
	Targetable:
		TargetTypes: Ground, Structure, Wormhole
	Armor:
		Type: Concrete
	Health:
		HP: 50000
	RallyPoint:
	WithDeathAnimation:
		UseDeathTypeSuffix: false
		CrushedSequence: die
		FallbackSequence: die
	WithMakeAnimation:
	LinkedProducerTarget:
		Mode: Exit
		Types: InfantrySQ, InfantryMQ, VehicleSQ, VehicleMQ
		RequiresCondition: !powerdown
	Sellable:
		SellSounds: cashturn.aud
	WithSpriteBody:
		RequiresCondition: !powerdown
	WithSpriteBody@Disabled:
		Name: disabled
		RequiresCondition: powerdown
		Sequence: faded
	ToggleConditionOnOrder:
		Condition: powerdown
		OrderName: PowerDown

# infiltrator stolen tech

# portal - cyberscrin
stolentech.port:
	Inherits@DUMMY: ^InvisibleDummy
	ProvidesPrerequisite:

# warp sphere - viper
stolentech.wsph:
	Inherits@DUMMY: ^InvisibleDummy
	ProvidesPrerequisite:

# gravity stabilizer - manticore
stolentech.grav:
	Inherits@DUMMY: ^InvisibleDummy
	ProvidesPrerequisite:
